#define IMGUI_DEFINE_MATH_OPERATORS
#include "GUI.h"
#include "../Components/Components.h"
#include <functional>
#include "../../Utils.h"
#include "../Components/nav_elements.h"
#include "../Fonts/icons.h"
#include "../Fonts/font_defines.h"
#include "../Fonts/custom_icons.h"
#include "../ImGui/blur/blur.hpp"
#include <iostream>
#include "../../Cheat Core/Features/Config/Config.h"
#include "../../Cheat Core/Settings/Settings.h"
#include <algorithm>  // for std::transform
#include <string.h>    // for strstr
#include "GUI_Helper.h"

// Custom icon definitions for missing icons
#define ICON_DIRECTION_LINE "\uea77"
#define ICON_USER_LINE "\uf409"
#define ICON_USER_UNFOLLOW_LINE "\uf3fe"
#define ICON_ROBOT_LINE "\uf157"
#define ICON_SPEED_LINE "\uf288"
#define ICON_USER_SETTINGS_LINE "\uf401"
#define ICON_GUN_LINE "\uee0e"
#define ICON_FOCUS_2_LINE "\uee99"
#define ICON_FOCUS_3_LINE "\uee9a"
#define ICON_CIRCLE_FILL "\uea80"
#define ICON_CIRCLE_LINE "\uea81"
#define ICON_BOX_LINE "\uea4e"
#define ICON_BOX_3D_FILL "\uebbc"
#define ICON_TEAM_LINE "\uf2fe"
#define ICON_GHOST_LINE "\uee11"
#define ICON_ID_CARD_LINE "\uef21"
#define ICON_MEDAL_LINE "\uefaa"
#define ICON_GAME_PAD_LINE "\uee08"
#define ICON_RULER_LINE "\uf167"
#define ICON_FONT_SIZE_LINE "\uee9c"
#define ICON_DOWNLOAD_LINE "\ued1d"
#define ICON_SHARE_LINE "\uf1e9"
#define ICON_DELETE_BIN_LINE "\uec45"
#define ICON_CHECKBOX_CIRCLE_FILL "\ueb9e"
#define ICON_SEARCH_LINE "\uf0d1"
#define ICON_ADD_LINE "\uea12"
#define ICON_SETTINGS_LINE "\uf1de"
#define ICON_CIRCLE_FILL "\uea80"

#define ICON_CORNER_LINE "\ueb03"
#define ICON_SHAPE_LINE "\uf1dc"
#define ICON_BORDER_RADIUS_LINE "\uea47"
#define ICON_LINE_HEIGHT_LINE "\uef57"
#define ICON_SKELETON_LINE "\uf217"
#define ICON_HEAD_LINE "\ueed2"
#define ICON_MEDICINE_BOTTLE_LINE "\uefae"
#define ICON_CAPSULE_LINE "\uea5a"
#define ICON_FIRST_AID_KIT_LINE "\uee73"
#define ICON_SHIELD_LINE "\uf1ea"
#define ICON_WATER_FLASH_LINE "\uf4e1"
#define ICON_BOTTLE_LINE "\uea49"
#define ICON_AMMO_LINE "\uea22"
#define ICON_BULLET_LINE "\uea56"
#define ICON_ROCKET_LINE "\uf14f"
#define ICON_TREASURE_CHEST_FILL "\uf39b"
#define ICON_CAR_LINE "\uea5d"
#define ICON_PINWHEEL_LINE "\uf0c3"
#define ICON_BOX_2_LINE "\uea4d"
#define ICON_KEYBOARD_LINE "\uef4a"
#define ICON_KEY_LINE "\uef46"
#define ICON_HOTKEY_LINE "\uef0a"
#define ICON_TOGGLE_LINE "\uf361"
#define ICON_SETTINGS_LINE "\uf1d7"
#define ICON_GLOBAL_LINE "\ueef7"
#define ICON_TRANSLATE_LINE "\uf347"
#define ICON_INFORMATION_LINE "\uef30"
#define ICON_ANIMATION_LINE "\uea2d"
#define ICON_SAVE_LINE "\uf17c"
#define ICON_UPLOAD_LINE "\uf3e9"
#define ICON_DOWNLOAD_LINE "\uec44"
#define ICON_RESTART_LINE "\uf149"
#define ICON_VERSION_LINE "\ueaeb"
#define ICON_CONTACTS_LINE "\ueb9f"
#define ICON_CALENDAR_LINE "\uea58"
#define ICON_MAIL_LINE "\uef8a"
#define ICON_FILE_LIST_3_LINE "\uedd7"

// Add missing icon definitions
#define ICON_WIDTH_FILL "\uf533"
#define ICON_FONT_SIZE_FILL "\uee9b"
#define ICON_WATER_FLASH_FILL "\uf4e0"
#define ICON_SPEED_FILL "\uf287"
#define ICON_SHELL_FILL "\uf1de"
#define ICON_ROBOT_FILL "\uf156"
#define ICON_PERSON_FILL "\uf21e"
#define ICON_KEY_FILL "\uef45"
#define ICON_IMAGE_FILL "\uef16"
#define ICON_GUN_FILL "\uee0d"
#define ICON_GAMEPAD_FILL "\uee07"
#define ICON_CROSSHAIR_FILL "\ueb07"
#define ICON_BULLET_FILL "\uea55"
#define ICON_BLUR_FILL "\uea48"
#define ICON_AMMO_FILL "\uea21"
#define ICON_RADAR_FILL "\uf12b"
#define ICON_HEART_FILL "\ueedd"
#define ICON_TEXT_FILL "\uf32e"
#define ICON_RULER_FILL "\uf166"
#define ICON_SHIELD_FILL "\uf1e9"
#define ICON_LINE_FILL "\uef52"
#define ICON_SQUARE_FILL "\uf290"
#define ICON_CHECKBOX_FILL "\uea78"
#define ICON_BODY_FILL "\uea4b"
#define ICON_ZOOM_IN_FILL "\uf54d"
#define ICON_PACKAGE_FILL "\uf0a6"
#define ICON_CAR_FILL "\uea5c"
#define ICON_LOCK_FILL "\uef69"
#define ICON_FOLDER_OPEN_FILL "\uee81"
#define ICON_COLOR_FILL "\ueb16"
#define ICON_INVENTORY_FILL "\uef35"
#define ICON_MENU_FILL "\uef86"
#define ICON_TRANSLATE_FILL "\uf346"
#define ICON_INFORMATION_FILL "\uef2f"
#define ICON_TOGGLE_FILL "\uf360"

//new
#define ICON_DELETE        "\uf5de"    // Delete/trash icon
#define ICON_DOWNLOAD      "\uf407"    // Download icon
#define ICON_UPLOAD        "\uf435"    // Upload icon
#define ICON_SHARE         "\uf3ef"    // Share icon
#define ICON_NEW           "\uf49e"    // Plus/new icon
#define ICON_HASHTAG       "\uf292"    // Hashtag icon
#define ICON_USER          "\uf2bd"    // User profile icon

// Enable logging for debugging
#define DEBUG_LOG 1

#if DEBUG_LOG
#define LOG_DEBUG(msg, ...) printf("[DEBUG] " msg "\n", ##__VA_ARGS__)
#else
#define LOG_DEBUG(msg, ...)
#endif

// External DirectX resources from main.cpp
extern IDXGISwapChain* g_pSwapChain;

CGui pGUi = CGui();

float accent_color[4] = { 0.f / 255.f, 145.f / 255.f, 255.f / 255.f, 1.00f };

// Animation helper methods
float CGui::EaseOutExpo(float x) {
    return x == 1.0f ? 1.0f : 1.0f - powf(2.0f, -10.0f * x);
}

float CGui::EaseInOutQuad(float x) {
    return x < 0.5f ? 2.0f * x * x : 1.0f - powf(-2.0f * x + 2.0f, 2.0f) / 2.0f;
}

// Gradient text rendering helper - relative to current window
void CGui::RenderGradientText(const char* text, ImVec2 pos, ImColor startColor, ImColor endColor, ImFont* font) {
    // Use current window draw list instead of foreground
    ImDrawList* drawList = ImGui::GetWindowDrawList();

    // Get current cursor position if pos is zero
    ImVec2 cursorPos = ImGui::GetCursorPos();
    if (pos.x == 0 && pos.y == 0) {
        pos = cursorPos;
    }

    // Calculate text size for gradient end position
    ImVec2 textSize = ImGui::CalcTextSize(text);

    // Set the cursor position
    ImGui::SetCursorPos(pos);

    // Get the absolute position for drawing
    ImVec2 textPos = ImGui::GetCursorScreenPos();

    // Horizontally center the text in window
    ImVec2 windowSize = ImGui::GetWindowSize();
    textPos.x = textPos.x + (windowSize.x - textSize.x) * 0.5f - ImGui::GetScrollX();

    // Push font if provided
    if (font) {
        ImGui::PushFont(font);
    }

    // Get vertex buffer index before drawing text
    const int vtx_idx_start = drawList->VtxBuffer.Size;

    // Draw the text (this adds vertices to the buffer)
    drawList->AddText(textPos, ImColor(1.0f, 1.0f, 1.0f, 1.0f), text);

    // Get vertex buffer index after drawing text
    const int vtx_idx_end = drawList->VtxBuffer.Size;

    // Apply gradient to the text vertices
    ImGui::ShadeVertsLinearColorGradientSetAlpha(
        drawList,
        vtx_idx_start,
        vtx_idx_end,
        textPos,
        ImVec2(textPos.x + textSize.x, textPos.y),
        startColor,
        endColor
    );

    // Pop font if pushed
    if (font) {
        ImGui::PopFont();
    }

    // Add spacing after text - advance cursor
    //ImGui::SetCursorPosY(pos.y + textSize.y);

    // Ensure ImGui knows this space was used (for layout)
    ImGui::Dummy(ImVec2(textSize.x, textSize.y));
}

// Gradient rectangle drawing helper for current window
void CGui::DrawGradientRect(ImRect rect, ImU32 color1, ImU32 color2, float thickness) {
    ImDrawList* drawList = ImGui::GetWindowDrawList();

    // Left part of the gradient
    const int vtx_idx_1 = drawList->VtxBuffer.Size;
    drawList->AddRectFilled(rect.Min, ImVec2(rect.GetCenter().x, rect.Max.y), color1);
    drawList->AddShadowRect(rect.Min, ImVec2(rect.GetCenter().x - 5.5f, rect.Max.y), color1, thickness, ImVec2(0, 0));
    const int vtx_idx_2 = drawList->VtxBuffer.Size;
    ImGui::ShadeVertsLinearColorGradientSetAlpha(drawList, vtx_idx_1, vtx_idx_2, rect.Min, ImVec2(rect.GetCenter().x, rect.Max.y), color1, color2);

    // Right part of the gradient
    const int vtx_idx_3 = drawList->VtxBuffer.Size;
    drawList->AddRectFilled(ImVec2(rect.GetCenter().x, rect.Min.y), rect.Max, color1);
    drawList->AddShadowRect(ImVec2(rect.GetCenter().x + 5.5f, rect.Min.y), rect.Max, color1, thickness, ImVec2(0, 0));
    const int vtx_idx_4 = drawList->VtxBuffer.Size;
    ImGui::ShadeVertsLinearColorGradientSetAlpha(drawList, vtx_idx_3, vtx_idx_4, ImVec2(rect.GetCenter().x, rect.Min.y), rect.Max, color2, color1);
}

// Render gradient separator line - relative to current window
void CGui::RenderGradientSeparator(float y_offset, float width_percentage = 0.75f) {
    // Get current window draw list
    ImDrawList* drawList = ImGui::GetWindowDrawList();

    // Save current cursor position
    ImVec2 cursorPos = ImGui::GetCursorPos();

    // Calculate separator position
    ImVec2 windowSize = ImGui::GetWindowSize();
    float line_height = 2.0f; // Height of the separator line

    // Calculate the width based on percentage of window width
    float line_width = (windowSize.x - ImGui::GetStyle().WindowPadding.x * 2) * width_percentage;

    // Set cursor position for the separator
    ImGui::SetCursorPosY(cursorPos.y + y_offset);

    // Get screen position for drawing
    ImVec2 lineStart = ImGui::GetCursorScreenPos();

    // Calculate center position for alignment
    float remaining_space = windowSize.x - ImGui::GetStyle().WindowPadding.x * 2 - line_width;
    float start_x_offset = remaining_space / 2.0f; // Center the line

    // Adjust lineStart to center the line
    lineStart.x += start_x_offset;

    // Draw the main filled rect for the separator line
    drawList->AddRectFilled(
        lineStart,
        ImVec2(lineStart.x + line_width, lineStart.y + line_height),
        ImGui::ColorConvertFloat4ToU32(ImVec4(accent_color[2], accent_color[1], accent_color[0], 1.0f))
    );

    // Create a rectangle for the gradient effect
    ImRect gradient_rect(
        lineStart,
        ImVec2(lineStart.x + line_width, lineStart.y + line_height)
    );

    // Apply the gradient effect
    DrawGradientRect(
        gradient_rect,
        ImColor(1.0f, 1.0f, 1.0f, 0.1f),
        ImGui::ColorConvertFloat4ToU32(ImVec4(accent_color[2], accent_color[1], accent_color[0], 1.0f)),
        35.0f
    );

    // Restore cursor position with an offset for spacing
    ImGui::SetCursorPosY(cursorPos.y + y_offset + line_height + 5.0f);
}

// Helper to render a section header that scrolls with the content
void CGui::RenderSectionHeader(const char* title, float titleY, float separatorY) {
    // Save cursor position for ImGui layout
    ImVec2 startCursorPos = ImGui::GetCursorPos();

    // Set cursor position for the title if specified
    if (titleY > 0) {
        ImGui::SetCursorPosY(titleY);
    }

    // Render the title with gradient
    RenderGradientText(
        title,
        ImVec2(0, 0), // Use current cursor position
        ImColor(accent_color[2], accent_color[1], accent_color[0], 1.0f),
        ImColor(1.0f, 1.0f, 1.0f, 1.0f)
    );

    // Render the separator line
    RenderGradientSeparator(0, 0.75f); // Use current cursor position

    // Add spacing after the section header
    ImGui::Dummy(ImVec2(0, 8));
}

void CGui::Initialize(ID3D11Device* g_pd3dDevice) {
    ImGuiIO& io = ImGui::GetIO();
    (void)io;
    ImFontConfig fontConfig;
    fontConfig.MergeMode = true;
    fontConfig.PixelSnapH = true;
    static ImFontConfig icons_config;
    icons_config.OversampleH = icons_config.OversampleV = 1;
    icons_config.MergeMode = true;
    icons_config.GlyphOffset.y = 6.5f;

    static ImFontConfig icomoon_config2;
    icomoon_config2.OversampleH = icomoon_config2.OversampleV = 1;
    icomoon_config2.MergeMode = true;
    icomoon_config2.GlyphOffset.y = 6.5f;

    auto fontPath = LoadChinese();
    std::string savepath = "C:\\BitCheats\\IconsFont.ttf";
    io.Fonts->AddFontFromMemoryTTF(&normal_font, sizeof(normal_font), 20, NULL, io.Fonts->GetGlyphRangesCyrillic());
    if (!fontPath.empty()) {
        io.Fonts->AddFontFromFileTTF(fontPath.c_str(), 20, &fontConfig, nav_elements::GetGlyphRangesChinese());
    }
    io.AddInputCharacter(0x410);
    io.AddInputCharacter(0x4E00);
    static ImWchar icomoon_ranges[] = { 0x1, 0x10FFFD, 0 };

    ico = io.Fonts->AddFontFromMemoryTTF(&icon, sizeof sizeof(icon), 23.0f);
    Personfont = io.Fonts->AddFontFromMemoryTTF(all_icons, sizeof(all_icons), 300.0f);
    tab_icons = io.Fonts->AddFontFromMemoryTTF(all_icons, sizeof(all_icons), 20.0f);

    static const ImWchar icons_ranges[] = { ICON_MIN_MS, ICON_MAX_16_MS, 0 };


    ImFontConfig small_cfg = icons_config;
    small_cfg.MergeMode = false;             // ← this is the key
    small_cfg.OversampleH = icons_config.OversampleV = 1;
    small_cfg.GlyphOffset.y = 6.5f;

    float baseSize = 55.0f * 1.3f / 2.0f;   // ≈ 35.75px
    float smallSize = 45.0f * 1.3f / 2.0f;   // ≈ 35.75px

    // big icons (merged into your default font)
    iconsBig = io.Fonts->AddFontFromFileTTF(
        savepath.c_str(), baseSize, &icons_config, icons_ranges);

    // small icons (stand-alone, separate atlas entry)
    iconsSmall = io.Fonts->AddFontFromFileTTF(
        savepath.c_str(), smallSize, &small_cfg, icons_ranges);


    nav_elements::Theme();

    // Initialize animation states
    menuAlpha = 0.0f;
    contentAlpha = 0.0f;
    tabTransitionProgress = 1.0f;
    isMenuOpen = false;
    lastTabChangeTime = ImGui::GetTime();

    // Initialize the configuration system
    InitializeConfigSystem();
}

void CGui::Render(bool& ShowMenu) {
    // Handle menu animation
    float currentTime = ImGui::GetTime();

    // Update menu opening/closing animation
    if (ShowMenu != isMenuOpen) {
        isMenuOpen = ShowMenu;
        if (isMenuOpen) {
            // Take snapshot of current settings when menu is opened
            g_ConfigSnapshot.CaptureSettings();

            // Clear notifications when menu is opened to fix the bug
            g_NotificationManager.ClearChanges();

            contentAlpha = 0.0f; // Reset content alpha when opening
            featureStartTime = ImGui::GetTime(); // Reset feature animation timer
        } else {
            // Track changes and save config when menu is hidden
            TrackConfigChanges();
        }
    }

    // Calculate menu animation
    if (isMenuOpen) {
        menuAlpha = ImClamp(menuAlpha + 0.1f, 0.0f, 1.0f);
    } else {
        menuAlpha = ImClamp(menuAlpha - 0.1f, 0.0f, 1.0f);
        if (menuAlpha <= 0.0f) {
            // Still render notifications even if menu is closed
            g_NotificationManager.ShowNotifications();
            return; // Exit if menu is fully closed
        }
    }

    // Content appearance animation
    if (isMenuOpen) {
        contentAlpha = ImClamp(contentAlpha + animationSpeed * 2.0f, 0.0f, 1.0f);
    }

    // Handle tab switching without fade animation
    if (currentTab != previousTab) {
        previousTab = currentTab;
        featureStartTime = ImGui::GetTime(); // Reset feature animation for new tab
    }

    // Calculate animated alpha
    float animMenuAlpha = EaseOutExpo(menuAlpha);

    // Set window transparency based on animation
    ImGui::SetNextWindowBgAlpha(0.95f * animMenuAlpha);

    // Size is fixed but position is not - allows dragging
    ImGui::SetNextWindowSize(ImVec2(1020, 635));

    ImGui::PushStyleVar(ImGuiStyleVar_Alpha, animMenuAlpha);
    ImGui::Begin("##MainWindow", &ShowMenu, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoScrollWithMouse);
    {
        gui.window_pos = ImGui::GetWindowPos();
        gui.window_size = ImGui::GetWindowSize();

        auto draw = ImGui::GetWindowDrawList();
        const auto& p = ImGui::GetWindowPos();
        ImGuiStyle& style = ImGui::GetStyle();

        // Navigation sidebar with animation
        ImGui::PushStyleVar(ImGuiStyleVar_Alpha, animMenuAlpha);

        // Logo/branding area
        ImGui::SetCursorPos(ImVec2(10, 25));
        ImGui::BeginChild("LogoArea", ImVec2(200, 100), false);
        {
            ImVec2 windowPos = ImGui::GetWindowPos();
            ImDrawList* drawList = ImGui::GetWindowDrawList();

            // Draw logo (blue circle with B letter)
            ImVec4 accentColor = ImVec4(0.0f, 0.58f, 1.0f, 1.0f);
            drawList->AddCircleFilled(
                ImVec2(windowPos.x + 80.0f, windowPos.y + 40.0f),
                30.0f,
                ImGui::ColorConvertFloat4ToU32(accentColor),
                32
            );

            // Add "BITCHEATS" text below logo
            ImGui::SetCursorPosY(80.0f);
            ImGui::SetCursorPosX(50.0f);
            ImGui::TextColored(ImVec4(1.0f, 1.0f, 1.0f, 1.0f), "BITCHEATS");
        }
        ImGui::EndChild();

        // Navigation tabs
        ImGui::SetCursorPos(ImVec2(10, 157));
        ImGui::BeginChild("Tabs", ImVec2(200, 465), true);
        {
            ImGui::Indent(10.0f);
            // Tab buttons with icons
            ImGui::SetCursorPos(ImVec2(10, 20));

            if (nav_elements::Tab("AIMBOT", ICON_MS_TARGET, &currentTab, ModernUI::NavigationPanel::TAB_AIMBOT)) {}
            if (nav_elements::Tab("VISUALS", ICON_MS_VISIBILITY, &currentTab, ModernUI::NavigationPanel::TAB_VISUALS)) {}
            if (nav_elements::Tab("ITEMS", ICON_MS_INVENTORY, &currentTab, ModernUI::NavigationPanel::TAB_ITEMS)) {}
            if (nav_elements::Tab("RADAR", ICON_MS_RADAR, &currentTab, ModernUI::NavigationPanel::TAB_RADAR)) {}
            if (nav_elements::Tab("SETTINGS", ICON_MS_SETTINGS, &currentTab, ModernUI::NavigationPanel::TAB_SETTINGS)) {}
            if (nav_elements::Tab("CONFIGS", ICON_MS_FOLDER, &currentTab, ModernUI::NavigationPanel::TAB_CONFIGS)) {}

            ImGui::Unindent(10.0f);
        }
        ImGui::EndChild();
        ImGui::PopStyleVar(); // Pop alpha style for navigation

        // Content area
        ImGui::SetCursorPos(ImVec2(220, 25));

        // Content area based on current tab (without fade animation)
        ImGui::BeginChild("ContentArea", ImVec2(787, 598), true);
        {
            ImGui::Indent(10.0f);
            ImGui::Dummy(ImVec2(0, 10));

            // Reset feature index for animation
            featureIndex = 0;

        switch (currentTab) {
            case ModernUI::NavigationPanel::TAB_AIMBOT:
                RenderAimbotTab();
                break;
            case ModernUI::NavigationPanel::TAB_VISUALS:
                RenderVisualsTab();
                break;
            case ModernUI::NavigationPanel::TAB_ITEMS:
                RenderMainTab();
                break;
            case ModernUI::NavigationPanel::TAB_RADAR:
                RenderRadarTab();
                break;
            case ModernUI::NavigationPanel::TAB_SETTINGS:
                RenderSettingsTab();
                break;
            case ModernUI::NavigationPanel::TAB_CONFIGS:
                RenderConfigsTab();
                break;
        }

            ImGui::Unindent(10.0f);
            ImGui::Dummy(ImVec2(100, 10));

            // Store the window position and add the 10px offset to x for the left side
            ImVec2 windowPos = ImGui::GetWindowPos();
            ImVec2 windowSize = ImGui::GetWindowSize();

            // Top gradient with adjusted left position
            ImGui::GetWindowDrawList()->AddRectFilledMultiColor(
                ImVec2(windowPos.x + 10, windowPos.y),  // Add 10px to x for left side
                ImVec2(windowPos.x + windowSize.x, windowPos.y + 30),
                gui.window_bg,
                gui.window_bg,
                func.GetColorWithAlpha(gui.window_bg, 0.f),
                func.GetColorWithAlpha(gui.window_bg, 0.f)
            );

            // Bottom gradient with adjusted left position
            ImGui::GetWindowDrawList()->AddRectFilledMultiColor(
                ImVec2(windowPos.x + 10, windowPos.y + windowSize.y - 30),  // Add 10px to x for left side
                ImVec2(windowPos.x + windowSize.x, windowPos.y + windowSize.y),
                func.GetColorWithAlpha(gui.window_bg, 0.f),
                func.GetColorWithAlpha(gui.window_bg, 0.f),
                gui.window_bg,
                gui.window_bg
            );
        }
        ImGui::EndChild();
    }
    ImGui::End();
    ImGui::PopStyleVar(); // Pop alpha style for window

    // Check if a color picker popup is actually open
    ImGuiContext& context = *GImGui;
    ImGuiStyle& style = ImGui::GetStyle();

    // Force the dark overlay to be visible when picker_active is set
    // This ensures the overlay appears immediately when the popup is opened
    static float dark_overlay = 0.f;

    // Check if any popup with "ColorPickerPopup" in the name is open
    bool isColorPickerPopupOpen = ImGui::IsPopupOpen("##ColorPickerPopup", ImGuiPopupFlags_AnyPopupId);

    // Also check windows directly as a fallback
    if (!isColorPickerPopupOpen) {
        for (int i = 0; i < context.Windows.Size; i++) {
            ImGuiWindow* window = context.Windows[i];
            if (window &&
                (window->Flags & ImGuiWindowFlags_Popup) &&
                window->WasActive &&
                window->Name &&
                strstr(window->Name, "ColorPickerPopup") != nullptr) {
                isColorPickerPopupOpen = true;
                break;
            }
        }
    }

    // If gui.picker_active is true but no popup is found, keep it true for a few frames
    // to ensure the overlay appears during popup opening animation
    static int delayCounter = 0;
    if (gui.picker_active && !isColorPickerPopupOpen) {
        if (delayCounter < 5) {
            delayCounter++;
            isColorPickerPopupOpen = true;
        } else {
            gui.picker_active = false;
            delayCounter = 0;
        }
    } else if (isColorPickerPopupOpen) {
        gui.picker_active = true;
        delayCounter = 0;
    }

    // Apply dark overlay animation
    dark_overlay = ImLerp(dark_overlay, gui.picker_active ? 0.75f : 0.f, GetAnimSpeed() * 3.0f);

    // Only draw overlay if it's visible
    if (dark_overlay > 0.01f) {
        for (ImGuiWindow* window : context.Windows)
        {
            if (!(window->Flags & ImGuiWindowFlags_Popup) && window != gui.combo_window && gui.picker_active) {
                window->DrawList->AddRectFilled(ImVec2(gui.window_pos.x, gui.window_pos.y), ImVec2(gui.window_pos.x + 1020, gui.window_pos.y + 635), func.GetColorWithAlpha(gui.window_bg, dark_overlay), style.WindowRounding, ImDrawFlags_RoundCornersAll);
            }
        }
    }

    // Sync settings with global variables to apply changes in real-time
    SettingsHelper::SyncSettings();

    // Show notifications if we have any changes (only if menu is not open)
    if (!isMenuOpen) {
        g_NotificationManager.ShowNotifications();
    }
}

// Helper method for feature animations
bool CGui::AnimateNextFeature() {
    const float featureDelay = 0.05f;  // Delay between features in seconds
    const float animationDuration = 0.3f;  // Total animation duration per feature

    float animationStartTime = featureStartTime + (featureIndex * featureDelay);
    float currentTime = ImGui::GetTime();
    float elapsed = currentTime - animationStartTime;

    // Feature is ready to be displayed with animation
    bool isAnimating = elapsed >= 0.0f && elapsed <= animationDuration;
    bool isVisible = elapsed >= 0.0f;

    if (isAnimating) {
        // Animate the feature position and alpha
        float progress = elapsed / animationDuration;
        float animatedOffset = (1.0f - EaseOutExpo(progress)) * 20.0f;

        // Apply offset animation to the element
        ImGui::SetCursorPosX(ImGui::GetCursorPosX() + animatedOffset);
    }

    featureIndex++;
    return isVisible;
}

void CGui::RenderAimbotTab() {
    // Aimbot Settings
    RenderSectionHeader("Aimbot Settings", 0, 0);

    // Main aimbot toggles
    if (Settings.Aim.AimbotConfig.ByWeapon) {
        Aimbots& weaponSettings = Settings.Aim.WeaponConfigs.weaponAimSettings[Settings.Config.ConfigMenu.selectedWeapon];

        // Main toggles
        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Enable Aimbot", &weaponSettings.Enable, "Activate the aimbot functionality^" ICON_MS_TARGET, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotEnable, &Keys.AimbotEnableMode);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Aim Lock", &weaponSettings.AimLock, "Keep aim locked on target^" ICON_MS_LOCK, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotAimLock, &Keys.AimbotAimLockMode);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Prediction", &weaponSettings.Predict, "Predict target movement^" ICON_MS_TRENDING_UP, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotPrediction, &Keys.AimbotPredictionMode);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Save Target", &weaponSettings.SaveTarget, "Remember last aimed target^" ICON_MS_SAVE, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotSaveTarget, &Keys.AimbotSaveTargetMode);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Visibility Check", &weaponSettings.VisibilityCheck, "Only aim at visible targets^" ICON_MS_VISIBILITY, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotVisibilityCheck, &Keys.AimbotVisibilityCheckMode);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Humanized Smooth", &weaponSettings.HumanizedSmooth, "Add human-like smoothing^" ICON_MS_PERSON, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotHumanizedSmooth, &Keys.AimbotHumanizedSmoothMode);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Ignore Knocked Players", &weaponSettings.IgnoreDowned, "Skip downed players^" ICON_MS_PERSON_OFF, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotIgnoreDowned, &Keys.AimbotIgnoreDownedMode);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Target Player AI", &weaponSettings.PlayerAi, "Include AI players as targets^" ICON_MS_SMART_TOY, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotPlayerAi, &Keys.AimbotPlayerAiMode);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Weapon Only", &weaponSettings.WeaponOnly, "Only activate when holding weapon^" ICON_MS_GAVEL, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotWeaponOnly, &Keys.AimbotWeaponOnlyMode);
        } else {
            // Main toggles
        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Enable Aimbot", &Settings.Aim.AimbotConfig.Enable, "Activate the aimbot functionality^" ICON_MS_TARGET, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotEnable, &Keys.AimbotEnableMode);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Aim Lock", &Settings.Aim.AimbotConfig.AimLock, "Keep aim locked on target^" ICON_MS_LOCK, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotAimLock, &Keys.AimbotAimLockMode);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Prediction", &Settings.Aim.AimbotConfig.Predict, "Predict target movement^" ICON_MS_TRENDING_UP, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotPrediction, &Keys.AimbotPredictionMode);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Save Target", &Settings.Aim.AimbotConfig.SaveTarget, "Remember last aimed target^" ICON_MS_SAVE, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotSaveTarget, &Keys.AimbotSaveTargetMode);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Visibility Check", &Settings.Aim.AimbotConfig.VisibilityCheck, "Only aim at visible targets^" ICON_MS_VISIBILITY, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotVisibilityCheck, &Keys.AimbotVisibilityCheckMode);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Humanized Smooth", &Settings.Aim.AimbotConfig.HumanizedSmooth, "Add human-like smoothing^" ICON_MS_PERSON, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotHumanizedSmooth, &Keys.AimbotHumanizedSmoothMode);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Ignore Knocked Players", &Settings.Aim.AimbotConfig.IgnoreDowned, ("Skip downed players^" + std::string(ICON_MS_PERSON_OFF)).c_str(), true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotIgnoreDowned, &Keys.AimbotIgnoreDownedMode);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Target Player AI", &Settings.Aim.AimbotConfig.PlayerAi, ("Include AI players as targets^" + std::string(ICON_MS_SMART_TOY)).c_str(), true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotPlayerAi, &Keys.AimbotPlayerAiMode);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("Weapon Only", &Settings.Aim.AimbotConfig.WeaponOnly, ("Only activate when holding weapon^" + std::string(ICON_MS_GAVEL)).c_str(), true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotWeaponOnly, &Keys.AimbotWeaponOnlyMode);
    }

    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Aimbot Configuration", 0, 0);

    // Aimbot configuration
    if (Settings.Aim.AimbotConfig.ByWeapon) {
        Aimbots& weaponSettings = Settings.Aim.WeaponConfigs.weaponAimSettings[Settings.Config.ConfigMenu.selectedWeapon];

        if (AnimateNextFeature())
            nav_elements::SliderInt("Max Distance", &weaponSettings.MaxDistance, 1, 1000, ("Maximum targeting distance^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), "%d m");

        const char* hitboxes[] = { "Head", "Neck", "Chest", "Pelvis", "Random" };
        if (AnimateNextFeature())
            nav_elements::Combo("Hitbox", &weaponSettings.HitBox, hitboxes, IM_ARRAYSIZE(hitboxes), ("Target body part^" + std::string(ICON_MS_BODY_PART)).c_str());

        if (AnimateNextFeature())
            nav_elements::SliderFloat("Smoothness", &weaponSettings.Smooth, 1.0f, 20.0f, ("Aim movement smoothness^" + std::string(ICON_MS_SPEED)).c_str(), "%.1f");

        if (AnimateNextFeature())
            nav_elements::SliderFloat("Humanized Amount", &weaponSettings.HumanizedSmoothPercent, 1.0f, 10.0f, ("Human-like aim behavior level^" + std::string(ICON_MS_PERSON_SETTINGS)).c_str(), "%.1f");
    } else {
        if (AnimateNextFeature())
            nav_elements::SliderInt("Max Distance", &Settings.Aim.AimbotConfig.MaxDistance, 1, 1000, ("Maximum targeting distance^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), "%d m");

        const char* hitboxes[] = { "Head", "Neck", "Chest", "Pelvis", "Random" };
        if (AnimateNextFeature())
            nav_elements::Combo("Hitbox", &Settings.Aim.AimbotConfig.HitBox, hitboxes, IM_ARRAYSIZE(hitboxes), ("Target body part^" + std::string(ICON_MS_BODY_PART)).c_str());

        if (AnimateNextFeature())
            nav_elements::SliderFloat("Smoothness", &Settings.Aim.AimbotConfig.Smooth, 1.0f, 20.0f, ("Aim movement smoothness^" + std::string(ICON_MS_SPEED)).c_str(), "%.1f");

        if (AnimateNextFeature())
            nav_elements::SliderFloat("Humanized Amount", &Settings.Aim.AimbotConfig.HumanizedSmoothPercent, 1.0f, 10.0f, ("Human-like aim behavior level^" + std::string(ICON_MS_PERSON_SETTINGS)).c_str(), "%.1f");
    }

    // Weapon selection section
    if (Settings.Aim.AimbotConfig.ByWeapon) {
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
        RenderSectionHeader("Weapon Selection", 0, 0);

        const char* weaponTypes[] = { "DMR", "SMG", "Shotgun", "Pistol", "AssaultRifle", "Sniper" };
        if (AnimateNextFeature())
            nav_elements::Combo("Weapon Type", &Settings.Config.ConfigMenu.selectedWeapon, weaponTypes, IM_ARRAYSIZE(weaponTypes), ("Select weapon for configuration^" + std::string(ICON_MS_WEAPONS)).c_str());
    }

    ImGui::Dummy(ImVec2(0, 10));
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Configure By Weapon", &Settings.Aim.AimbotConfig.ByWeapon, ("Enable per-weapon configuration^" + std::string(ICON_MS_TUNE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);

    // Visual indicators section
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Aimbot Visuals", 0, 0);

    // FOV settings
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Draw FOV", &Settings.Aim.AimbotConfig.DrawFov, ("Show field of view circle^" + std::string(ICON_MS_CIRCLE)).c_str(), true, GetAimbotColorStates("Draw FOV"), &Keys.AimbotDrawFov, &Keys.AimbotDrawFovMode);

    if (Settings.Aim.AimbotConfig.DrawFov) {
        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("FOV Filled", &Settings.Aim.AimbotConfig.DrawFovFilled, ("Fill the FOV circle^" + std::string(ICON_MS_CIRCLE_FILLED)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("FOV Outline", &Settings.Aim.AimbotConfig.DrawFovOutline, ("Draw FOV circle outline^" + std::string(ICON_MS_CIRCLE_OUTLINE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);

        if (AnimateNextFeature())
            nav_elements::CheckboxComponent("FOV RGB Mode", &Settings.Aim.AimbotConfig.DrawFovRGB, ("Cycle RGB colors for FOV^" + std::string(ICON_MS_PALETTE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);

        if (AnimateNextFeature())
            nav_elements::SliderFloat("FOV Size", &Settings.Aim.AimbotConfig.FOV, 1.0f, 500.0f, ("Adjust FOV circle size^" + std::string(ICON_MS_ZOOM_IN)).c_str(), "%.1f");
    }

    // Crosshair settings
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Draw Crosshair", &Settings.Aim.AimbotConfig.DrawCrossHair, ("Show custom crosshair^" + std::string(ICON_MS_ADD_CIRCLE)).c_str(), true, GetAimbotColorStates("Draw Crosshair"), &Keys.AimbotDrawCrossHair, &Keys.AimbotDrawCrossHairMode);

    if (Settings.Aim.AimbotConfig.DrawCrossHair) {
        const char* crosshairTypes[] = { "Plus", "Circle Cross", "Cross Dot", "Square", "Circle Dot", "X Dot" };
        if (AnimateNextFeature())
            nav_elements::Combo("Crosshair Type", &Settings.Aim.AimbotConfig.DrawCrossHairType, crosshairTypes, IM_ARRAYSIZE(crosshairTypes), ("Select crosshair style^" + std::string(ICON_MS_CROSSHAIRS)).c_str());

        if (AnimateNextFeature())
            nav_elements::SliderFloat("Crosshair Size", &Settings.Aim.AimbotConfig.DrawSize, 1.0f, 50.0f, ("Adjust crosshair size^" + std::string(ICON_MS_ZOOM_IN)).c_str(), "%.1f");
    }

    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Draw Target Line", &Settings.Aim.AimbotConfig.DrawTarget, ("Show line to current target^" + std::string(ICON_MS_TRENDING_FLAT)).c_str(), true, GetAimbotColorStates("Draw Target Line"), &Keys.AimbotDrawTarget, &Keys.AimbotDrawTargetMode);

    if (AnimateNextFeature())
        nav_elements::SliderFloat("Drawing Thickness", &Settings.Aim.AimbotConfig.DrawThickness, 1.0f, 5.0f, ("Adjust line thickness^" + std::string(ICON_MS_BRUSH)).c_str(), "%.1f");
}

void CGui::RenderVisualsTab() {
    // Players section
    RenderSectionHeader("Players", 0, 0);

    // Players settings
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Enable Player ESP", &Settings.Esp.Player.Enable, ("Toggle player ESP^" + std::string(ICON_MS_VISIBILITY)).c_str(), true, std::vector<nav_elements::ColorState>{}, &Keys.PlayerEspEnable, &Keys.PlayerEspEnableMode);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Bounding Box", &Settings.Esp.Player.Box, ("Draw bounding box^" + std::string(ICON_MS_SQUARE)).c_str(), true, GetPlayerEspColorStates("Bounding Box"), &Keys.PlayerEspBox, &Keys.PlayerEspBoxMode);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Corner Box", &Settings.Esp.Player.Box, ("Draw corner box^" + std::string(ICON_MS_CROP_SQUARE)).c_str(), true, GetPlayerEspColorStates("Corner Box"), &Keys.PlayerEspBox, &Keys.PlayerEspBoxMode);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Skeleton", &Settings.Esp.Player.Skeleton, ("Draw skeleton^" + std::string(ICON_MS_SKELETON)).c_str(), true, GetPlayerEspColorStates("Skeleton"), &Keys.PlayerEspSkeleton, &Keys.PlayerEspSkeletonMode);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Names", &Settings.Esp.Player.NickName, ("Show player names^" + std::string(ICON_MS_PERSON)).c_str(), true, GetPlayerEspColorStates("Names"), &Keys.PlayerEspNickName, &Keys.PlayerEspNickNameMode);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Distance", &Settings.Esp.Player.Distance, ("Show distance to players^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), true, GetPlayerEspColorStates("Distance"), &Keys.PlayerEspDistance, &Keys.PlayerEspDistanceMode);
   /* if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Health", &Settings.Esp.Player.Health, ("Show player health^" + std::string(ICON_MS_FAVORITE)).c_str(), true, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Shield", &Settings.Esp.Player.Shield, ("Show player shield^" + std::string(ICON_MS_SHIELD)).c_str(), true, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);*/
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Snapline", &Settings.Esp.Player.Lines, ("Draw snaplines^" + std::string(ICON_MS_TRENDING_FLAT)).c_str(), true, GetPlayerEspColorStates("Snapline"), &Keys.PlayerEspLines, &Keys.PlayerEspLinesMode);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Weapon", &Settings.Esp.Player.Weapon, ("Show player weapons^" + std::string(ICON_MS_WEAPONS)).c_str(), true, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);

    ImGui::Dummy(ImVec2(0, 5));

    if (AnimateNextFeature())
        nav_elements::SliderInt("Max Distance", &Settings.Esp.Player.MaxDistance, 1, 500, ("Maximum render distance^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), "%d m");
    if (AnimateNextFeature())
        nav_elements::SliderFloat("Box Thickness", &Settings.Esp.Player.BoxThickness, 1.0f, 5.0f, ("Adjust box thickness^" + std::string(ICON_MS_LINE_WEIGHT)).c_str(), "%.1f");
    if (AnimateNextFeature())
        nav_elements::SliderFloat("Corner Box Thickness", &Settings.Esp.Player.BoxThickness, 1.0f, 5.0f, ("Adjust corner box thickness^" + std::string(ICON_MS_LINE_WEIGHT)).c_str(), "%.1f");
    if (AnimateNextFeature())
        nav_elements::SliderFloat("Text Size", &Settings.Esp.Player.FontSize, 10.0f, 25.0f, ("Adjust text size^" + std::string(ICON_MS_FORMAT_SIZE)).c_str(), "%.1f");
    if (AnimateNextFeature())
        nav_elements::SliderFloat("Snapline Thickness", &Settings.Esp.Player.LinesThickness, 1.0f, 5.0f, ("Adjust snapline thickness^" + std::string(ICON_MS_LINE_WEIGHT)).c_str(), "%.1f");
    if (AnimateNextFeature())
        nav_elements::SliderFloat("Skeleton Thickness", &Settings.Esp.Player.SkeletonThickness, 1.0f, 5.0f, ("Adjust skeleton thickness^" + std::string(ICON_MS_LINE_WEIGHT)).c_str(), "%.1f");
}

void CGui::RenderMainTab() {
    // Tab selectors for loot categories
    static int lootTab = 0;
    ImGui::BeginGroup();

    if (nav_elements::HorizontalTab("Consumables", &lootTab, 0)) {}
    ImGui::SameLine(0, 10);
    if (nav_elements::HorizontalTab("Weapons", &lootTab, 1)) {}
    ImGui::SameLine(0, 10);
    if (nav_elements::HorizontalTab("Ammo", &lootTab, 2)) {}
    ImGui::SameLine(0, 10);
    if (nav_elements::HorizontalTab("Other Items", &lootTab, 3)) {}

    ImGui::EndGroup();

        ImGui::Dummy(ImVec2(0, 10));

    // Show selected tab content
    switch (lootTab) {
        case 0: // Consumables
            RenderConsumablesTab();
            break;
        case 1: // Weapons
            RenderWeaponsTab();
            break;
        case 2: // Ammo
            RenderAmmoTab();
            break;
        case 3: // Other Items
            RenderOtherItemsTab();
            break;
    }
}

void CGui::RenderConsumablesTab() {
        // Consumables section
        RenderSectionHeader("Consumables", 0, 0);

        // Consumable settings
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Enable Consumables ESP", &Settings.Esp.Item.Consumable.Enable, ("Toggle consumables ESP^" + std::string(ICON_MS_VISIBILITY)).c_str(), true, std::vector<nav_elements::ColorState>{}, &Keys.ItemConsumableEnable, &Keys.ItemConsumableEnableMode);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Show Names", &Settings.Esp.Item.Consumable.Name, ("Display item names^" + std::string(ICON_MS_PERSON)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Show Distance", &Settings.Esp.Item.Consumable.Distance, ("Show distance to items^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Show Icons", &Settings.Esp.Item.Consumable.Icons, ("Display item icons^" + std::string(ICON_MS_IMAGE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);

    if (AnimateNextFeature())
        nav_elements::SliderInt("Max Distance", &Settings.Esp.Item.Consumable.MaxDistance, 1, 300, ("Maximum render distance^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), "%d m");
    if (AnimateNextFeature())
        nav_elements::SliderFloat("Font Size", &Settings.Esp.Item.Consumable.FontSize, 10.0f, 25.0f, ("Adjust text size^" + std::string(ICON_MS_FORMAT_SIZE)).c_str(), "%.1f");
    if (AnimateNextFeature())
        nav_elements::SliderFloat("Icon Size", &Settings.Esp.Item.Consumable.IconSize, 5.0f, 40.0f, ("Adjust icon size^" + std::string(ICON_MS_ZOOM_IN)).c_str(), "%.1f");

    // Consumable items selection
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
        RenderSectionHeader("Consumables to Display", 0, 0);

    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Bandages", &Settings.Esp.Item.Consumable.Bandages, ("Show bandages^" + std::string(ICON_MS_HEALING)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Medkit", &Settings.Esp.Item.Consumable.Medkit, ("Show medkits^" + std::string(ICON_MS_MEDICAL_SERVICES)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Small Shield", &Settings.Esp.Item.Consumable.SmallShieldPotion, ("Show small shields^" + std::string(ICON_MS_SHIELD)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Big Shield", &Settings.Esp.Item.Consumable.ShieldPotion, ("Show big shields^" + std::string(ICON_MS_SHIELD)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Chug Splash", &Settings.Esp.Item.Consumable.ChugSplash, ("Show chug splash^" + std::string(ICON_MS_WATER_DROP)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Nitro Splash", &Settings.Esp.Item.Consumable.NitroSplash, ("Show nitro splash^" + std::string(ICON_MS_SCIENCE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("FlowBerry Fizz", &Settings.Esp.Item.Consumable.FlowBerryFizz, ("Show flowberry fizz^" + std::string(ICON_MS_BUBBLE_CHART)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Nuka-Cola", &Settings.Esp.Item.Consumable.NukaCola, ("Show nuka-cola^" + std::string(ICON_MS_LOCAL_DRINK)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
}

void CGui::RenderWeaponsTab() {
        // Weapons section
        RenderSectionHeader("Weapons", 0, 0);

        // Weapon settings
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Enable Weapons ESP", &Settings.Esp.Item.Weapon.Enable, ("Toggle weapons ESP^" + std::string(ICON_MS_VISIBILITY)).c_str(), true, std::vector<nav_elements::ColorState>{}, &Keys.ItemWeaponEnable, &Keys.ItemWeaponEnableMode);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Show Names", &Settings.Esp.Item.Weapon.Name, ("Display weapon names^" + std::string(ICON_MS_PERSON)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Show Distance", &Settings.Esp.Item.Weapon.Distance, ("Show distance to weapons^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Show Icons", &Settings.Esp.Item.Weapon.Icons, ("Display weapon icons^" + std::string(ICON_MS_IMAGE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);

    if (AnimateNextFeature())
        nav_elements::SliderInt("Max Distance", &Settings.Esp.Item.Weapon.MaxDistance, 1, 300, ("Maximum render distance^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), "%d m");
    if (AnimateNextFeature())
        nav_elements::SliderFloat("Font Size", &Settings.Esp.Item.Weapon.FontSize, 10.0f, 25.0f, ("Adjust text size^" + std::string(ICON_MS_FORMAT_SIZE)).c_str(), "%.1f");
    if (AnimateNextFeature())
        nav_elements::SliderFloat("Icon Size", &Settings.Esp.Item.Weapon.IconSize, 5.0f, 40.0f, ("Adjust icon size^" + std::string(ICON_MS_ZOOM_IN)).c_str(), "%.1f");

        // Game mode selection
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
        RenderSectionHeader("Game Mode", 0, 0);

        const char* gameModes[] = { "Remix Battle Royale", "Other Modes" };
    if (AnimateNextFeature())
        nav_elements::Combo("Game Mode", &Settings.Config.ConfigMenu.GameMode, gameModes, IM_ARRAYSIZE(gameModes), ("Select game mode^" + std::string(ICON_MS_SPORTS_ESPORTS)).c_str());

        // Weapon display selection
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
        RenderSectionHeader("Weapons to Display", 0, 0);

        if (Settings.Config.ConfigMenu.GameMode == 0) { // Remix Battle Royale
        if (AnimateNextFeature())
            nav_elements::Checkbox("New Pump Shotgun", &Settings.Esp.Item.Weapon.NewPump_Shotgun, ("Show pump shotgun^" + std::string(ICON_MS_WEAPONS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("OG Pump Shotgun", &Settings.Esp.Item.Weapon.OGPump_Shotgun, ("Show OG pump shotgun^" + std::string(ICON_MS_WEAPONS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Submachine Gun", &Settings.Esp.Item.Weapon.SubmachineGun, ("Show submachine gun^" + std::string(ICON_MS_WEAPONS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Dual Pistols", &Settings.Esp.Item.Weapon.DualPistols, ("Show dual pistols^" + std::string(ICON_MS_WEAPONS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Rapid Fire SMG", &Settings.Esp.Item.Weapon.RapidFireSMG, ("Show rapid fire SMG^" + std::string(ICON_MS_WEAPONS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Suppressed SMG", &Settings.Esp.Item.Weapon.SuppressedSMG, ("Show suppressed SMG^" + std::string(ICON_MS_WEAPONS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Suppressed Assault Rifle", &Settings.Esp.Item.Weapon.SuppressedAssaultRifle, ("Show suppressed AR^" + std::string(ICON_MS_WEAPONS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Heavy Sniper Rifle", &Settings.Esp.Item.Weapon.HeavySniperRifle, ("Show heavy sniper^" + std::string(ICON_MS_CROSSHAIRS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Semi-Auto Sniper Rifle", &Settings.Esp.Item.Weapon.SemiAutomaticSniperRifle, ("Show semi-auto sniper^" + std::string(ICON_MS_CROSSHAIRS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Grenade Launcher", &Settings.Esp.Item.Weapon.GrenadeLauncher, ("Show grenade launcher^" + std::string(ICON_MS_ROCKET)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Remote Explosives", &Settings.Esp.Item.Weapon.RemoteExplosives, ("Show remote explosives^" + std::string(ICON_MS_DANGEROUS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Regular Grenades", &Settings.Esp.Item.Weapon.RegularGrenades, ("Show grenades^" + std::string(ICON_MS_DANGEROUS)).c_str());
        } else { // Other Modes
        if (AnimateNextFeature())
            nav_elements::Checkbox("Ranger Pistol", &Settings.Esp.Item.Weapon.RangerPistol, ("Show ranger pistol^" + std::string(ICON_MS_WEAPONS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Harbinger SMG", &Settings.Esp.Item.Weapon.HarbingerSMG, ("Show harbinger SMG^" + std::string(ICON_MS_WEAPONS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Thunder Burst SMG", &Settings.Esp.Item.Weapon.ThunderBurstSMG, ("Show thunder burst SMG^" + std::string(ICON_MS_WEAPONS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Warforged Assault Rifle", &Settings.Esp.Item.Weapon.WarforgedAssaultRifle, ("Show warforged AR^" + std::string(ICON_MS_WEAPONS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Tactical Assault Rifle", &Settings.Esp.Item.Weapon.TacticalAssaultRifle, ("Show tactical AR^" + std::string(ICON_MS_WEAPONS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Combat Assault Rifle", &Settings.Esp.Item.Weapon.CombatAssaultRifle, ("Show combat AR^" + std::string(ICON_MS_WEAPONS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Combat Shotgun", &Settings.Esp.Item.Weapon.CombatShotgun, ("Show combat shotgun^" + std::string(ICON_MS_WEAPONS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Gatekeeper Shotgun", &Settings.Esp.Item.Weapon.GatekeeperShotgun, ("Show gatekeeper shotgun^" + std::string(ICON_MS_WEAPONS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Hammer Pump Shotgun", &Settings.Esp.Item.Weapon.HammerPumpShotgun, ("Show hammer pump shotgun^" + std::string(ICON_MS_WEAPONS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Huntress DMR", &Settings.Esp.Item.Weapon.HuntressDMR, ("Show huntress DMR^" + std::string(ICON_MS_CROSSHAIRS)).c_str());
        if (AnimateNextFeature())
            nav_elements::Checkbox("Heavy Impact Sniper", &Settings.Esp.Item.Weapon.HeavyImpactSniperRifle, ("Show heavy impact sniper^" + std::string(ICON_MS_CROSSHAIRS)).c_str());
        }

        // Rarity filters
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
        RenderSectionHeader("Rarity Filter", 0, 0);

        const char* rarities[] = { "Common", "Uncommon", "Rare", "Epic", "Legendary", "Mythic" };
        for (int i = 0; i < 6; i++) {
        if (i > 0) ImGui::SameLine();
        if (AnimateNextFeature())
            nav_elements::Checkbox(rarities[i], &Settings.Esp.Item.Weapon.Rarity[i], (std::string(rarities[i]) + "^" + std::string(ICON_MS_DIAMOND)).c_str());
    }
}

void CGui::RenderAmmoTab() {
        // Ammo section
        RenderSectionHeader("Ammo", 0, 0);

        // Ammo settings
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Enable Ammo ESP", &Settings.Esp.Item.Ammo.Enable, ("Toggle ammo ESP^" + std::string(ICON_MS_VISIBILITY)).c_str(), true, std::vector<nav_elements::ColorState>{}, &Keys.ItemAmmoEnable, &Keys.ItemAmmoEnableMode);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Show Names", &Settings.Esp.Item.Ammo.Name, ("Display ammo names^" + std::string(ICON_MS_PERSON)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Show Distance", &Settings.Esp.Item.Ammo.Distance, ("Show distance to ammo^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Show Icons", &Settings.Esp.Item.Ammo.Icons, ("Display ammo icons^" + std::string(ICON_MS_IMAGE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);

    if (AnimateNextFeature())
        nav_elements::SliderInt("Max Distance", &Settings.Esp.Item.Ammo.MaxDistance, 1, 300, ("Maximum render distance^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), "%d m");
    if (AnimateNextFeature())
        nav_elements::SliderFloat("Font Size", &Settings.Esp.Item.Ammo.FontSize, 10.0f, 25.0f, ("Adjust text size^" + std::string(ICON_MS_FORMAT_SIZE)).c_str(), "%.1f");
    if (AnimateNextFeature())
        nav_elements::SliderFloat("Icon Size", &Settings.Esp.Item.Ammo.IconSize, 5.0f, 40.0f, ("Adjust icon size^" + std::string(ICON_MS_ZOOM_IN)).c_str(), "%.1f");

        // Ammo types
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
        RenderSectionHeader("Ammo Types to Display", 0, 0);

    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Light Ammo", &Settings.Esp.Item.Ammo.AmmoLight, ("Show light ammo^" + std::string(ICON_MS_BULLET)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Medium Ammo", &Settings.Esp.Item.Ammo.AmmoMedium, ("Show medium ammo^" + std::string(ICON_MS_BULLET)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Heavy Ammo", &Settings.Esp.Item.Ammo.AmmoHeavy, ("Show heavy ammo^" + std::string(ICON_MS_BULLET)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Shells", &Settings.Esp.Item.Ammo.AmmoShells, ("Show shells^" + std::string(ICON_MS_BULLET)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Rockets", &Settings.Esp.Item.Ammo.AmmoRockets, ("Show rockets^" + std::string(ICON_MS_ROCKET)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
}

void CGui::RenderOtherItemsTab() {
        // Other items section
        RenderSectionHeader("Other Items", 0, 0);

        // Other items settings
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Enable Other Items ESP", &Settings.Esp.Item.Other.Enable, ("Toggle other items ESP^" + std::string(ICON_MS_VISIBILITY)).c_str(), true, std::vector<nav_elements::ColorState>{}, &Keys.ItemOtherEnable, &Keys.ItemOtherEnableMode);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Show Names", &Settings.Esp.Item.Other.Name, ("Display item names^" + std::string(ICON_MS_PERSON)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Show Distance", &Settings.Esp.Item.Other.Distance, ("Show distance to items^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Show Icons", &Settings.Esp.Item.Other.Icons, ("Display item icons^" + std::string(ICON_MS_IMAGE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);

    if (AnimateNextFeature())
        nav_elements::SliderInt("Max Distance", &Settings.Esp.Item.Other.MaxDistance, 1, 300, ("Maximum render distance^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), "%d m");
    if (AnimateNextFeature())
        nav_elements::SliderFloat("Font Size", &Settings.Esp.Item.Other.FontSize, 10.0f, 25.0f, ("Adjust text size^" + std::string(ICON_MS_FORMAT_SIZE)).c_str(), "%.1f");
    if (AnimateNextFeature())
        nav_elements::SliderFloat("Icon Size", &Settings.Esp.Item.Other.IconSize, 5.0f, 40.0f, ("Adjust icon size^" + std::string(ICON_MS_ZOOM_IN)).c_str(), "%.1f");

        // Other item types
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
        RenderSectionHeader("Other Items to Display", 0, 0);

    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Chests", &Settings.Esp.Item.Other.Chest, ("Show chests^" + std::string(ICON_MS_INVENTORY)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Vehicles", &Settings.Esp.Item.Other.Vehicle, ("Show vehicles^" + std::string(ICON_MS_DIRECTIONS_CAR)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Llamas", &Settings.Esp.Item.Other.Llama, ("Show llamas^" + std::string(ICON_MS_PETS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Supply Drops", &Settings.Esp.Item.Other.SupplyDrop, ("Show supply drops^" + std::string(ICON_MS_INVENTORY_2)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);
}

void CGui::RenderRadarTab() {
    // Radar section
    RenderSectionHeader("Radar", 0, 0);

    // Radar settings
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Enable Radar", &Settings.Radar.RadarConfig.Enable, "Toggle radar^" ICON_MS_RADAR, true, GetRadarColorStates("Enable Radar"), &Keys.RadarEnable, &Keys.RadarEnableMode);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Show Distance", &Settings.Radar.RadarConfig.Distance, "Show distance to players^" ICON_MS_STRAIGHTEN, true, std::vector<nav_elements::ColorState>{}, &Keys.RadarDistance, &Keys.RadarDistanceMode);

        // Radar type selection
        ImGui::Dummy(ImVec2(0, 5));
        const char* radarTypes[] = { "Circle", "Rectangle" };
    if (AnimateNextFeature())
        nav_elements::Combo("Radar Type", &Settings.Radar.RadarConfig.RadarType, radarTypes, IM_ARRAYSIZE(radarTypes), "Select radar type^" ICON_MS_RADAR);

        // Radar position
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Radar Position", 0, 0);

    if (AnimateNextFeature())
        nav_elements::SliderInt("Position X", &Settings.Radar.RadarConfig.PositionX, 0, 1920, "Adjust radar horizontal position^" ICON_MS_TRENDING_FLAT, "%d px");
    if (AnimateNextFeature())
        nav_elements::SliderInt("Position Y", &Settings.Radar.RadarConfig.PositionY, 0, 1080, "Adjust radar vertical position^" ICON_MS_TRENDING_FLAT, "%d px");

        // Radar size
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Radar Size", 0, 0);

        if (Settings.Radar.RadarConfig.RadarType == 0) { // Circle radar
        if (AnimateNextFeature())
            nav_elements::SliderInt("Circle Size", &Settings.Radar.RadarConfig.CirleSize, 50, 300, "Adjust radar circle size^" ICON_MS_ZOOM_IN, "%d px");
        } else { // Rectangle radar
        if (AnimateNextFeature())
            nav_elements::SliderInt("Rectangle Size", &Settings.Radar.RadarConfig.RectangleSize, 50, 400, "Adjust radar rectangle size^" ICON_MS_ZOOM_IN, "%d px");
    }

    // Other radar settings
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Radar Settings", 0, 0);

    if (AnimateNextFeature())
        nav_elements::SliderInt("Max Distance", &Settings.Radar.RadarConfig.MaxDistance, 1, 100, "Maximum radar range^" ICON_MS_STRAIGHTEN, "%d m");
    if (AnimateNextFeature())
        nav_elements::SliderInt("Distance Font Size", &Settings.Radar.RadarConfig.DistanceFontSize, 8, 20, "Adjust distance text size^" ICON_MS_FORMAT_SIZE, "%d");

    // Player highlight options
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Player Highlight Options", 0, 0);

    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Highlight Visible Players", &Settings.Radar.RadarConfig.VisibleColor, "Highlight visible players^" ICON_MS_VISIBILITY, true, std::vector<nav_elements::ColorState>{}, &Keys.RadarVisibleColor, &Keys.RadarVisibleColorMode);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Highlight Closest Player", &Settings.Radar.RadarConfig.ClosestColor, "Highlight closest player^" ICON_MS_PERSON, true, std::vector<nav_elements::ColorState>{}, &Keys.RadarClosestColor, &Keys.RadarClosestColorMode);
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Highlight Players Aiming At You", &Settings.Radar.RadarConfig.AimingAtMeColor, "Highlight players aiming at you^" ICON_MS_CROSSHAIRS, true, std::vector<nav_elements::ColorState>{}, &Keys.RadarAimingAtMeColor, &Keys.RadarAimingAtMeColorMode);

        // Radar colors
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Radar Colors", 0, 0);

 /*   if (AnimateNextFeature())
        nav_elements::ColorEdit("Visible Players", Settings.Colors.PlayerColors.RadarVisible, "Visible players color^" ICON_COLOR_FILL);
    if (AnimateNextFeature())
        nav_elements::ColorEdit("Non-Visible Players", Settings.Colors.PlayerColors.RadarNonVisible, "Non-visible players color^" ICON_COLOR_FILL);
    if (AnimateNextFeature())
        nav_elements::ColorEdit("Knocked Visible Players", Settings.Colors.PlayerColors.KnockedRadarVisible, "Knocked visible players color^" ICON_COLOR_FILL);
    if (AnimateNextFeature())
        nav_elements::ColorEdit("Knocked Non-Visible Players", Settings.Colors.PlayerColors.KnockedRadarNonVisible, "Knocked non-visible players color^" ICON_COLOR_FILL);*/
}

void CGui::RenderSettingsTab() {
    // Settings section
    RenderSectionHeader("Settings", 0, 0);

    // Key binds
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Key Bindings", 0, 0);

    // Example of using the new CheckboxComponent
    static bool playerEspEnabled = false;
    static int playerEspKey = 0;
    static bool playerEspMode = false;
    static float visibleColor[4] = { 0.0f, 1.0f, 0.0f, 1.0f }; // Green
    static float invisibleColor[4] = { 1.0f, 0.0f, 0.0f, 1.0f }; // Red
    static float knockedColor[4] = { 0.0f, 0.0f, 1.0f, 1.0f }; // Blue

    if (AnimateNextFeature()) {
        std::vector<nav_elements::ColorState> colorStates = {
            { "Visible", { Settings.Colors.PlayerColors.BoxVisible[0], Settings.Colors.PlayerColors.BoxVisible[1], Settings.Colors.PlayerColors.BoxVisible[2], Settings.Colors.PlayerColors.BoxVisible[3] }, Settings.Colors.PlayerColors.BoxVisible },
            { "Non-Visible", { Settings.Colors.PlayerColors.BoxNonVisible[0], Settings.Colors.PlayerColors.BoxNonVisible[1], Settings.Colors.PlayerColors.BoxNonVisible[2], Settings.Colors.PlayerColors.BoxNonVisible[3] }, Settings.Colors.PlayerColors.BoxNonVisible },
            { "Knocked", { Settings.Colors.PlayerColors.KnockedBoxVisible[0], Settings.Colors.PlayerColors.KnockedBoxVisible[1], Settings.Colors.PlayerColors.KnockedBoxVisible[2], Settings.Colors.PlayerColors.KnockedBoxVisible[3] }, Settings.Colors.PlayerColors.KnockedBoxVisible }
        };

        nav_elements::CheckboxComponent("Player ESP", &playerEspEnabled, "Toggle drawing of players", true, colorStates, &playerEspKey, &playerEspMode);
    }
    //if (AnimateNextFeature())
    //    nav_elements::Keybind("ESP Toggle", &Keys.Keys.TogglePlayers, "Toggle ESP visibility^" ICON_EYE_FILL);
    //if (AnimateNextFeature())
    //    nav_elements::Keybind("Items Toggle", &Keys.Keys.ToggleItems, "Toggle items ESP^" ICON_INVENTORY_FILL);
    //if (AnimateNextFeature())
    //    nav_elements::Keybind("Radar Toggle", &Keys.Keys.ToggleRadar, "Toggle radar visibility^" ICON_RADAR_FILL);
    //if (AnimateNextFeature())
    //    nav_elements::Keybind("Panic Mode", &Keys.Keys.TogglePanicMode, "Toggle panic mode^" ICON_KEY_FILL);

    //
    //    // Configuration management
    //    ImGui::Dummy(ImVec2(0, 10));
    //    ImGui::Text("Configuration");
    //    ImGui::Dummy(ImVec2(0, 5));
    //
    //if (AnimateNextFeature()) {
    //    if (nav_elements::Button("Load Config", ImVec2(150, 30), "Load configuration from file^" ICON_FOLDER_OPEN_FILL)) {
    //        SettingsHelper::LoadSettings();
    //    }
    //    }
    //
    //    ImGui::SameLine();
    //
    //if (AnimateNextFeature()) {
    //    if (nav_elements::Button("Save Config", ImVec2(150, 30), "Save current configuration^" ICON_SAVE_FILL)) {
    //        SettingsHelper::SaveSettings();
    //    }
    //}
    //
    //if (AnimateNextFeature()) {
    //    if (nav_elements::Button("Reset Settings", ImVec2(150, 30), "Reset all settings to default^" ICON_RESTART_LINE)) {
    //        SettingsHelper::ResetSettings();
    //    }
    //    }

    // Notification Settings
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Notification Settings", 0, 0);

    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Enable Notifications", &g_NotificationSettings.Enable, "Show notifications for settings changes^" ICON_MS_NOTIFICATIONS, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);

    if (AnimateNextFeature())
        nav_elements::SliderFloat("Notification Duration", &g_NotificationSettings.DisplayDuration, 1.0f, 20.0f, "How long notifications appear^" ICON_MS_TIMER, "%.1f s");

    // UI Settings
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("UI Settings", 0, 0);

    // Language selection
    const char* languages[] = { "English", "French", "German", "Russian", "Spanish", "Chinese" };
    if (AnimateNextFeature())
        nav_elements::Combo("Language", &Settings.Config.ConfigMenu.Language, languages, IM_ARRAYSIZE(languages), "Select language^" ICON_MS_TRANSLATE);

    // Feature definitions/tooltips
    if (AnimateNextFeature())
        nav_elements::CheckboxComponent("Show Feature Descriptions", &Settings.Config.ConfigMenu.FeaturesDefinition, "Show feature descriptions^" ICON_MS_INFO, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr);

    // Animation settings
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Animation Settings", 0, 0);

    if (AnimateNextFeature())
        nav_elements::SliderFloat("Animation Speed", &animationSpeed, 0.01f, 0.3f, "Adjust animation speed^" ICON_MS_SPEED, "%.2f");

        // About section
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("About", 0, 0);

    ImGui::Text("BitCheats Fortnite v4.2.1");
    ImGui::Text("© 2025 BitCheats Team");
}

// Forward declare RenderConfigsTab function

void CGui::RenderConfigsTab() {
    // Main header section with search bar
    pGUi.RenderSectionHeader("Configurations", 0, 0);

    // Search bar
    static char searchBuffer[128] = "";
    ImGui::Dummy(ImVec2(0, 5));

    ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(8, 8));

    // Get current window and draw list
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    ImDrawList* drawList = window->DrawList;

    // Set up search bar with custom styling - 35% of width
    float searchBarWidth = ImGui::GetContentRegionAvail().x * 0.35f;
    ImGui::SetNextItemWidth(searchBarWidth);

    // Get position and size for custom drawing
    ImVec2 pos = ImGui::GetCursorScreenPos();
    ImVec2 size = ImVec2(searchBarWidth, ImGui::GetFrameHeight());
    ImRect rect(pos, ImVec2(pos.x + size.x, pos.y + size.y));

    // Custom background and animation
    struct SearchBarAnimation {
        float element_opacity = 0.0f;
        float line_opacity = 0.0f;
        bool is_typing = false;
        float typing_anim = 0.0f;
        ImVec4 border_color = ImVec4(1.0f, 1.0f, 1.0f, 0.1f);
    };

    static std::map<ImGuiID, SearchBarAnimation> search_anim_map;
    ImGuiID id = window->GetID("##ConfigSearch");
    bool hovered = ImGui::IsMouseHoveringRect(rect.Min, rect.Max);
    bool active = ImGui::IsItemActive();

    auto it_anim = search_anim_map.find(id);
    if (it_anim == search_anim_map.end()) {
        search_anim_map.insert({id, {}});
        it_anim = search_anim_map.find(id);
    }

    // Check if user is typing
    static float last_input_time = ImGui::GetTime();
    if (ImGui::IsItemActive() && ImGui::GetIO().InputQueueCharacters.Size > 0) {
        it_anim->second.is_typing = true;
        last_input_time = ImGui::GetTime();
    }

    // Reset typing animation after a delay
    if (it_anim->second.is_typing && ImGui::GetTime() - last_input_time > 1.0f) {
        it_anim->second.is_typing = false;
    }

    // Apply the requested animation for background - match config background
    it_anim->second.element_opacity = ImLerp(it_anim->second.element_opacity,
                                           (active ? 0.04f : hovered ? 0.01f : 0.0f),
                                           0.07f * (1.0f - ImGui::GetIO().DeltaTime));

    // Apply the requested animation for bottom line
    it_anim->second.line_opacity = ImLerp(it_anim->second.line_opacity,
                                        (active || hovered) ? 1.0f : 0.0f,
                                        0.07f * (1.0f - ImGui::GetIO().DeltaTime));

    // Typing animation
    it_anim->second.typing_anim = ImLerp(it_anim->second.typing_anim,
                                        it_anim->second.is_typing ? 1.0f : 0.0f,
                                        0.1f * (1.0f - ImGui::GetIO().DeltaTime));

    // Update border color - blue when active
    if (active) {
        it_anim->second.border_color = ImVec4(0.2f, 0.4f, 1.0f, 0.8f);
    } else {
        it_anim->second.border_color = ImVec4(ImColor(255, 255, 255, 10));
    }

    // Draw background - match config background
    drawList->AddRectFilled(rect.Min, rect.Max, ImColor(0.12f, 0.12f, 0.15f, 0.8f), 3.0f);

    // Draw animated glow on top of background
    drawList->AddRectFilled(rect.Min, rect.Max, ImColor(1.0f, 1.0f, 1.0f, it_anim->second.element_opacity), 3.0f);

    // Draw border - match config border
    drawList->AddRect(rect.Min, rect.Max, ImGui::ColorConvertFloat4ToU32(it_anim->second.border_color), 3.0f, 0, 1.0f);

    // Draw bottom line with animation
    if (it_anim->second.line_opacity > 0.01f) {
        const float lineHeight = 2.0f;
        const float linePadding = 5.0f;
        ImVec2 lineStart(rect.Min.x + linePadding, rect.Max.y - lineHeight - 2.0f);
        ImVec2 lineEnd(rect.Max.x - linePadding, lineStart.y + lineHeight);

        // Shadow opacity based on hover/select state
        float shadowOpacity = active ? 1.0f : 0.6f; // 100% when selected, 60% when hovered

        // Shadow for horizontal line
        drawList->AddShadowRect(
            lineStart,
            lineEnd,
            ImColor(accent_color[2], accent_color[1], accent_color[0], shadowOpacity * it_anim->second.line_opacity),
            15.f,
            ImVec2(0, 2),
            ImDrawFlags_RoundCornersAll,
            40.f
        );

        // Main glow line
        drawList->AddRectFilled(
            lineStart,
            lineEnd,
            ImColor(accent_color[2], accent_color[1], accent_color[0], it_anim->second.line_opacity),
            360.f,
            ImDrawFlags_RoundCornersAll
        );
    }

    // Input text with transparent background
    ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
    if (ImGui::InputTextWithHint("##ConfigSearch", "Search by name or config ID", searchBuffer, IM_ARRAYSIZE(searchBuffer))) {
        g_ConfigSystem.searchQuery = searchBuffer;
    }

    ImGui::SameLine();

    // Search button using the new IconButton function with better icon
    if (nav_elements::IconButton("Search", ICON_MS_SEARCH, ImVec2(120, 36), ImColor(gui.main))) {
        // Search functionality is handled by input text already
    }

    // Position the Create New Config button on the right side of the search bar
    float windowWidth = ImGui::GetContentRegionAvail().x;
    ImGui::SameLine(windowWidth - 180); // Position at the right side with some margin

    // Create New Config button with Share functionality using the new IconButton function with better icon
    if (pGUi.AnimateNextFeature()) {
        if (nav_elements::IconButton("New Config", ICON_MS_ADD_CIRCLE, ImVec2(170, 36), ImColor(gui.main))) {
            g_ConfigSystem.showSharePopup = true;
            g_ConfigSystem.shareConfigName = "";
            g_ConfigSystem.shareConfigType = "";
        }
    }

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();

    ImGui::Dummy(ImVec2(0, 10));

    // Refresh current configs
    static bool initialized = false;
    if (!initialized) {
        InitializeConfigSystem();
        initialized = true;
    }

    ImGui::Dummy(ImVec2(0, 5));

    // Config entry containers with improved styling
    const float entryHeight = 75.0f; // Slightly taller for better spacing
    const float windowWidth2 = ImGui::GetContentRegionAvail().x;
    const float entryPadding = 10.0f; // Double padding between elements

    // Make sure we have configs
    if (g_ConfigSystem.configs.empty()) {
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "No configurations found. Create one to get started.");
    }

    // Calculate animations once to avoid jitter
    static float animation_time = 0.0f;
    animation_time += ImGui::GetIO().DeltaTime;
    float pulse = (sinf(animation_time * 2.5f) * 0.5f + 0.5f) * 0.3f + 0.7f; // Pulse between 0.7 and 1.0

    // Display config entries
    for (int i = 0; i < g_ConfigSystem.configs.size(); i++) {
        const ConfigFile& config = g_ConfigSystem.configs[i];

        // Filter by search query if exists
        if (!g_ConfigSystem.searchQuery.empty()) {
            std::string lowerName = config.name;
            std::string lowerType = config.type;
            std::string lowerSearch = g_ConfigSystem.searchQuery;

            // Convert to lowercase for case-insensitive comparison
            std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
            std::transform(lowerType.begin(), lowerType.end(), lowerType.begin(), ::tolower);
            std::transform(lowerSearch.begin(), lowerSearch.end(), lowerSearch.begin(), ::tolower);

            if (lowerName.find(lowerSearch) == std::string::npos &&
                lowerType.find(lowerSearch) == std::string::npos) {
                continue; // Skip if not matching search
            }
        }

        // Create a unique ID for each config entry
        ImGui::PushID(i);

        // Config entry background
        ImVec2 entryPos = ImGui::GetCursorScreenPos();
        ImVec2 entrySize(windowWidth2, entryHeight);
        ImDrawList* drawList = ImGui::GetWindowDrawList();

        // Get if entry is hovered
        bool entryHovered = ImGui::IsMouseHoveringRect(
            entryPos,
            ImVec2(entryPos.x + entrySize.x, entryPos.y + entrySize.y)
        );

        // Animation structure for config entries
        struct ConfigEntryAnimation {
            float element_opacity = 0.0f;
            float line_opacity = 0.0f;
        };

        static std::map<ImGuiID, ConfigEntryAnimation> config_anim_map;
        ImGuiID configId = window->GetID(("##ConfigEntry" + std::to_string(i)).c_str());

        auto config_anim = config_anim_map.find(configId);
        if (config_anim == config_anim_map.end()) {
            config_anim_map.insert({configId, {}});
            config_anim = config_anim_map.find(configId);
        }

        // Apply the requested animation for background - match CheckboxComponent styling
        config_anim->second.element_opacity = ImLerp(config_anim->second.element_opacity,
                                                  (config.isSelected ? 0.08f : entryHovered ? 0.04f : 0.02f),
                                                  0.14f * (1.0f - ImGui::GetIO().DeltaTime));

        // Apply the requested animation for bottom line
        config_anim->second.line_opacity = ImLerp(config_anim->second.line_opacity,
                                               (config.isSelected || entryHovered) ? 1.0f : 0.0f,
                                               0.07f * (1.0f - ImGui::GetIO().DeltaTime));

        // Draw background with improved colors to match user preferences
        ImColor backgroundColor = config.isSelected || entryHovered ?
            ImColor(25, 19, 15, 155) : func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f);

        drawList->AddRectFilled(
            entryPos,
            ImVec2(entryPos.x + entrySize.x, entryPos.y + entrySize.y),
            backgroundColor,
            3.0f
        );

        // Apply the requested border with improved colors
        ImColor borderColor = entryHovered ?
            gui.checkboxstrokeactive : func.GetColorWithAlpha(gui.checkboxstroke, 0.3f);

        drawList->AddRect(
            entryPos,
            ImVec2(entryPos.x + entrySize.x, entryPos.y + entrySize.y),
            borderColor,
            3.0f, 0, 1.0f
        );

        // Draw bottom line with animation
        if (config_anim->second.line_opacity > 0.01f) {
            const float lineHeight = 2.0f;
            const float linePadding = 5.0f;
            ImVec2 lineStart(entryPos.x + linePadding, entryPos.y + entrySize.y - lineHeight - 2.0f);
            ImVec2 lineEnd(entryPos.x + entrySize.x - linePadding, lineStart.y + lineHeight);

            // Shadow opacity based on hover/select state
            float shadowOpacity = config.isSelected ? 1.0f : 0.6f; // 100% when selected, 60% when hovered

            // Shadow for horizontal line
            drawList->AddShadowRect(
                lineStart,
                lineEnd,
                ImColor(accent_color[2], accent_color[1], accent_color[0], shadowOpacity * config_anim->second.line_opacity),
                15.f,
                ImVec2(0, 2),
                ImDrawFlags_RoundCornersAll,
                40.f
            );

            // Main glow line
            drawList->AddRectFilled(
                lineStart,
                lineEnd,
                ImColor(accent_color[2], accent_color[1], accent_color[0], config_anim->second.line_opacity),
                360.f,
                ImDrawFlags_RoundCornersAll
            );
        }

        // If selected, add blue accent line at bottom
        if (config.isSelected) {
            const float lineHeight = 2.0f;
            const float linePadding = 8.0f;
            ImVec2 lineStart(entryPos.x + linePadding, entryPos.y + entrySize.y - lineHeight - 2.0f);
            ImVec2 lineEnd(entryPos.x + entrySize.x - linePadding, lineStart.y + lineHeight);

            // Blue glow effect
            ImColor lineColor(0.2f, 0.4f, 1.0f, 0.8f * pulse);

            // Shadow for horizontal line
            drawList->AddShadowRect(
                lineStart,
                lineEnd,
                lineColor,
                15.f,
                ImVec2(0, 2),
                ImDrawFlags_RoundCornersAll,
                40.f
            );

            // Main glow line
            drawList->AddRectFilled(
                lineStart,
                lineEnd,
                lineColor,
                360.f,
                ImDrawFlags_RoundCornersAll
            );
        }

        // Button positions on the right
        float buttonWidth = 120.0f;
        float iconButtonWidth = 40.0f;
        float buttonHeight = 36.0f; // Increased height for better vertical alignment
        float padding = 10.0f;
        float buttonY = entryPos.y + (entryHeight - buttonHeight) * 0.5f; // Center buttons vertically

        // Config name with downloads icon below
        ImGui::SetCursorScreenPos(ImVec2(entryPos.x + 15, entryPos.y + 10));
        ImGui::TextColored(ImVec4(1.0f, 1.0f, 1.0f, 1.0f), "%s", config.name.c_str());

        // Downloads icon below config name
        ImGui::PushFont(iconsBig);
        ImVec2 downloadIconPos = ImVec2(entryPos.x + 15, entryPos.y + 35);
        ImGui::SetCursorScreenPos(downloadIconPos);
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), ICON_DOWNLOAD_LINE);
        ImGui::PopFont();

        // Downloads count
        ImGui::SetCursorScreenPos(ImVec2(downloadIconPos.x + 25, downloadIconPos.y));
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "%d", config.isDefault ? 1024 : 128);

        // Creator icon and name
        ImGui::PushFont(iconsBig);
        ImVec2 creatorIconPos = ImVec2(entryPos.x + 150, entryPos.y + 10);
        ImGui::SetCursorScreenPos(creatorIconPos);
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), ICON_USER_LINE);
        ImGui::PopFont();

        // Creator name
        ImGui::SetCursorScreenPos(ImVec2(creatorIconPos.x + 25, creatorIconPos.y));
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "@%s", config.isDefault ? "BitCheats" : "Creator");

        // Config type below creator
        ImGui::SetCursorScreenPos(ImVec2(creatorIconPos.x, entryPos.y + 40));
        ImGui::TextColored(ImVec4(0.6f, 0.6f, 0.6f, 1.0f), "Type: %s", config.type.c_str());

        // Last modified date
        char timeBuffer[64];
        std::time_t time = std::chrono::system_clock::to_time_t(config.lastModified);
        std::tm tm;
        localtime_s(&tm, &time);
        std::strftime(timeBuffer, sizeof(timeBuffer), "%Y-%m-%d %H:%M", &tm);

        ImGui::SetCursorScreenPos(ImVec2(entryPos.x + entrySize.x - 150, entryPos.y + 40));
        ImGui::TextColored(ImVec4(0.6f, 0.6f, 0.6f, 1.0f), "Modified: %s", timeBuffer);

        // Status indicator if selected
        if (config.isSelected) {
            ImVec2 statusPos = ImVec2(entryPos.x + entrySize.x - 80, entryPos.y + 10);
            ImGui::SetCursorScreenPos(statusPos);
            ImGui::PushFont(iconsBig);
            ImGui::TextColored(ImVec4(0.2f, 0.8f, 0.2f, 1.0f), ICON_CHECKBOX_CIRCLE_FILL);
            ImGui::PopFont();
        }

        // Add clickable region for the left part of the entry only - not overlapping with buttons
        float buttonsWidth = buttonWidth * 2 + iconButtonWidth + padding * 4;
        float clickableWidth = entrySize.x - buttonsWidth;

        // Save current cursor position
        ImVec2 originalCursorPos = ImGui::GetCursorScreenPos();

        // Set cursor to the beginning of the entry
        ImGui::SetCursorScreenPos(entryPos);

        // Create invisible button that only covers the left part of the entry
        ImGui::InvisibleButton("##ConfigEntryBackground", ImVec2(clickableWidth, entrySize.y));

        // Check if the entry is clicked to select the config
        if (ImGui::IsItemClicked()) {
            // Select this config
            SelectConfiguration(i);
        }

        // Restore cursor position for the buttons
        ImGui::SetCursorScreenPos(originalCursorPos);

        // Download button (right side, first button)
        ImVec2 downloadButtonPos = ImVec2(entryPos.x + entrySize.x - buttonWidth - padding, buttonY);
        ImGui::SetCursorScreenPos(downloadButtonPos);

        // Use the new IconButton function for Download/Load button with better icon
        if (nav_elements::IconButton(
            config.isDownloaded ? "Load" : "Download",
            config.isDownloaded ? ICON_MS_CHECK_CIRCLE : ICON_MS_DOWNLOAD,
            ImVec2(buttonWidth, buttonHeight),
            config.isDownloaded ? ImColor(0.2f, 0.8f, 0.2f) : ImColor(0.2f, 0.6f, 1.0f)
        )) {
            if (config.isDownloaded) {
                // Load config functionality
                SelectConfiguration(i);
            } else {
                // Download config functionality - use non-const reference
                ConfigFile& nonConstConfig = g_ConfigSystem.configs[i];
                nonConstConfig.isDownloaded = true;
                // Show notification
                g_NotificationManager.AddChange("Configurations", "Config Downloaded", false, true);
            }
        }

        // Share button (left of download button)
        ImVec2 shareButtonPos = ImVec2(downloadButtonPos.x - buttonWidth - padding, buttonY);
        ImGui::SetCursorScreenPos(shareButtonPos);

        // Use the new IconButton function for Share button with better icon
        if (nav_elements::IconButton("Share", ICON_MS_SHARE, ImVec2(buttonWidth, buttonHeight), ImColor(0.0f, 0.7f, 0.3f))) {
            // Open share popup with config name pre-filled
            g_ConfigSystem.showSharePopup = true;
            g_ConfigSystem.shareConfigName = config.name + "_Shared";
            g_ConfigSystem.shareConfigType = config.type;
        }

        // Delete button (icon only) - positioned to the left of Share button
        ImVec2 deleteButtonPos = ImVec2(shareButtonPos.x - iconButtonWidth - padding, buttonY);
        ImGui::SetCursorScreenPos(deleteButtonPos);

        // Use the new IconOnlyButton function for Delete button with proper red delete icon
        if (nav_elements::IconOnlyButton(ICON_MS_DELETE, ImVec2(iconButtonWidth, buttonHeight), ImColor(0.9f, 0.2f, 0.2f))) {
            if (!config.isDefault) {
                // Delete config
                DeleteConfiguration(config.filename);
            }
        }

        // Move cursor for next entry with double padding
        ImGui::SetCursorScreenPos(ImVec2(entryPos.x, entryPos.y + entryHeight + entryPadding * 2));

        ImGui::PopID();
    }

    // Share/Create Config Popup - identical to settings button popup
    static bool popupJustOpened = false;
    if (g_ConfigSystem.showSharePopup && !popupJustOpened) {
        // Set picker_active to trigger dark overlay (same as settings popup)
        gui.picker_active = true;

        // Open popup with unique ID
        if (!ImGui::IsPopupOpen("##CreateConfigPopup")) {
            ImGui::OpenPopup("##CreateConfigPopup");
            popupJustOpened = true;
        }
    }

    // Center the popup in the menu window (identical to settings popup)
    ImVec2 center(gui.window_pos.x + gui.window_size.x * 0.5f, gui.window_pos.y + gui.window_size.y * 0.5f);
    ImGui::SetNextWindowPos(center, ImGuiCond_Always, ImVec2(0.5f, 0.5f));

    if (ImGui::BeginPopup("##CreateConfigPopup", ImGuiWindowFlags_NoMove)) {
        // Set focus to the window when hovered (same as settings popup)
        if (ImGui::IsWindowHovered(ImGuiHoveredFlags_AllowWhenBlockedByPopup |
                                  ImGuiHoveredFlags_AllowWhenBlockedByActiveItem |
                                  ImGuiHoveredFlags_ChildWindows))
            ImGui::SetWindowFocus();

        // Close the popup when pressing Escape (same as settings popup)
        if (ImGui::IsKeyPressed(ImGuiKey_Escape))
        {
            gui.picker_active = false;
            g_ConfigSystem.showSharePopup = false;
            popupJustOpened = false;
            ImGui::CloseCurrentPopup();
        }

        // Close the popup when clicking outside (same as settings popup)
        bool clicked_outside = ImGui::IsMouseClicked(ImGuiMouseButton_Left) &&
                              !ImGui::IsWindowHovered(ImGuiHoveredFlags_AnyWindow |
                                                     ImGuiHoveredFlags_AllowWhenBlockedByPopup);
        if (clicked_outside && !ImGui::IsAnyItemHovered())
        {
            gui.picker_active = false;
            g_ConfigSystem.showSharePopup = false;
            popupJustOpened = false;
            ImGui::CloseCurrentPopup();
        }

        // Apply the exact same styling as settings popup
        ImGui::PushStyleColor(ImGuiCol_PopupBg, func.ImColorToImVec4(gui.window_bg));
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));
        ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, ImGui::GetStyle().FrameRounding);

        // Title with icon
        ImGui::PushFont(iconsBig);
        ImGui::Text(ICON_MS_ADD_CIRCLE);
        ImGui::PopFont();
        ImGui::SameLine();
        ImGui::Text("Create Configuration");
        ImGui::Separator();
        ImGui::Spacing();

        static char nameBuffer[128] = "";
        static char typeBuffer[64] = "";
        static bool hasError = false;
        static std::string errorMessage = "";

        // Pre-fill from the system state with safety checks
        if (ImGui::IsWindowAppearing()) {
            try {
                if (!g_ConfigSystem.shareConfigName.empty()) {
                    strncpy_s(nameBuffer, g_ConfigSystem.shareConfigName.c_str(), sizeof(nameBuffer) - 1);
                    nameBuffer[sizeof(nameBuffer) - 1] = '\0'; // Ensure null termination
                }
                if (!g_ConfigSystem.shareConfigType.empty()) {
                    strncpy_s(typeBuffer, g_ConfigSystem.shareConfigType.c_str(), sizeof(typeBuffer) - 1);
                    typeBuffer[sizeof(typeBuffer) - 1] = '\0'; // Ensure null termination
                }
                hasError = false;
                errorMessage = "";
            } catch (...) {
                hasError = true;
                errorMessage = "Failed to initialize popup data";
            }
        }

        // Show error message if any
        if (hasError) {
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.3f, 0.3f, 1.0f));
            ImGui::Text("Error: %s", errorMessage.c_str());
            ImGui::PopStyleColor();
            ImGui::Spacing();
        }

        // Config name input with improved styling
        ImGui::Text("Configuration Name:");
        ImGui::Spacing();

        // Custom styled input field
        ImVec2 inputPos = ImGui::GetCursorScreenPos();
        ImVec2 inputSize = ImVec2(ImGui::GetContentRegionAvail().x, 35);
        ImRect inputRect(inputPos, ImVec2(inputPos.x + inputSize.x, inputPos.y + inputSize.y));

        // Draw input background
        ImGui::GetWindowDrawList()->AddRectFilled(
            inputRect.Min,
            inputRect.Max,
            func.GetColorWithAlpha(gui.background, 0.3f),
            3.0f
        );

        // Draw blue top border
        ImGui::GetWindowDrawList()->AddRectFilled(
            inputRect.Min,
            ImVec2(inputRect.Max.x, inputRect.Min.y + 2),
            gui.main,
            3.0f,
            ImDrawFlags_RoundCornersTop
        );

        // Draw border
        ImGui::GetWindowDrawList()->AddRect(
            inputRect.Min,
            inputRect.Max,
            func.GetColorWithAlpha(gui.stroke, 0.3f),
            3.0f,
            0,
            1.0f
        );

        // Input field with transparent background
        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
        ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
        ImGui::PushStyleColor(ImGuiCol_FrameBgActive, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
        ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(10, 8));
        ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, 3.0f);

        ImGui::SetNextItemWidth(inputSize.x);
        bool nameChanged = ImGui::InputTextWithHint("##ConfigName", "Enter configuration name...", nameBuffer, IM_ARRAYSIZE(nameBuffer));

        ImGui::PopStyleVar(2);
        ImGui::PopStyleColor(3);

        // Validate name input
        if (nameChanged) {
            hasError = false;
            errorMessage = "";

            // Check for empty name
            if (strlen(nameBuffer) == 0) {
                hasError = true;
                errorMessage = "Configuration name cannot be empty";
            }
            // Check for invalid characters
            else {
                std::string name = nameBuffer;
                if (name.find_first_of("<>:\"/\\|?*") != std::string::npos) {
                    hasError = true;
                    errorMessage = "Configuration name contains invalid characters";
                }
            }
        }

        ImGui::Spacing();

        // Type input field with improved styling
        ImGui::Text("Configuration Type:");
        ImGui::Spacing();

        // Custom styled input field for type
        ImVec2 typeInputPos = ImGui::GetCursorScreenPos();
        ImVec2 typeInputSize = ImVec2(ImGui::GetContentRegionAvail().x, 35);
        ImRect typeInputRect(typeInputPos, ImVec2(typeInputPos.x + typeInputSize.x, typeInputPos.y + typeInputSize.y));

        // Draw input background
        ImGui::GetWindowDrawList()->AddRectFilled(
            typeInputRect.Min,
            typeInputRect.Max,
            func.GetColorWithAlpha(gui.background, 0.3f),
            3.0f
        );

        // Draw blue top border
        ImGui::GetWindowDrawList()->AddRectFilled(
            typeInputRect.Min,
            ImVec2(typeInputRect.Max.x, typeInputRect.Min.y + 2),
            gui.main,
            3.0f,
            ImDrawFlags_RoundCornersTop
        );

        // Draw border
        ImGui::GetWindowDrawList()->AddRect(
            typeInputRect.Min,
            typeInputRect.Max,
            func.GetColorWithAlpha(gui.stroke, 0.3f),
            3.0f,
            0,
            1.0f
        );

        // Input field with transparent background
        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
        ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
        ImGui::PushStyleColor(ImGuiCol_FrameBgActive, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
        ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(10, 8));
        ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, 3.0f);

        ImGui::SetNextItemWidth(typeInputSize.x);
        ImGui::InputTextWithHint("##ConfigType", "e.g., Rage, Legit, Balanced...", typeBuffer, IM_ARRAYSIZE(typeBuffer));

        ImGui::PopStyleVar(2);
        ImGui::PopStyleColor(3);

        ImGui::Spacing();
        ImGui::Separator();
        ImGui::Spacing();

        // Center the buttons
        float buttonWidth = 140.0f;
        float spacing = 10.0f;
        float totalWidth = buttonWidth * 2 + spacing;
        float startX = (ImGui::GetContentRegionAvail().x - totalWidth) / 2;
        ImGui::SetCursorPosX(startX);

        // Disable buttons if there's an error or empty name
        bool canCreate = !hasError && strlen(nameBuffer) > 0;

        if (!canCreate) {
            ImGui::PushStyleVar(ImGuiStyleVar_Alpha, 0.5f);
        }

        // Save local button using the new IconButton function with better icon
        if (nav_elements::IconButton("Create Local", ICON_MS_SAVE, ImVec2(buttonWidth, 36), ImColor(0.2f, 0.6f, 1.0f)) && canCreate) {
            try {
                g_ConfigSystem.shareConfigName = nameBuffer;
                g_ConfigSystem.shareConfigType = strlen(typeBuffer) > 0 ? typeBuffer : "Custom";

                // Create local config with error handling
                if (CreateConfiguration(g_ConfigSystem.shareConfigName, g_ConfigSystem.shareConfigType)) {
                    // Close popup and reset state (same as settings popup)
                    gui.picker_active = false;
                    g_ConfigSystem.showSharePopup = false;
                    popupJustOpened = false;
                    ImGui::CloseCurrentPopup();

                    // Clear buffers for next time
                    memset(nameBuffer, 0, sizeof(nameBuffer));
                    memset(typeBuffer, 0, sizeof(typeBuffer));
                    hasError = false;
                    errorMessage = "";

                    // Show notification
                    g_NotificationManager.AddChange("Configurations", "Config Created", false, true);
                } else {
                    hasError = true;
                    // Check if it's a duplicate name error
                    bool isDuplicate = false;
                    for (const auto& config : g_ConfigSystem.configs) {
                        if (config.name == g_ConfigSystem.shareConfigName) {
                            isDuplicate = true;
                            break;
                        }
                    }
                    errorMessage = isDuplicate ?
                        "Configuration with this name already exists" :
                        "Failed to create configuration file";
                }
            } catch (...) {
                hasError = true;
                errorMessage = "Unexpected error occurred while creating configuration";
            }
        }

        ImGui::SameLine();

        // Share to cloud button using the new IconButton function with better icon
        if (nav_elements::IconButton("Share to Cloud", ICON_MS_CLOUD_UPLOAD, ImVec2(buttonWidth, 36), ImColor(0.0f, 0.7f, 0.3f)) && canCreate) {
            try {
                g_ConfigSystem.shareConfigName = nameBuffer;
                g_ConfigSystem.shareConfigType = strlen(typeBuffer) > 0 ? typeBuffer : "Custom";

                // Share config to cloud with error handling
                if (ShareConfiguration(g_ConfigSystem.shareConfigName, g_ConfigSystem.shareConfigType)) {
                    // Close popup and reset state (same as settings popup)
                    gui.picker_active = false;
                    g_ConfigSystem.showSharePopup = false;
                    popupJustOpened = false;
                    ImGui::CloseCurrentPopup();

                    // Clear buffers for next time
                    memset(nameBuffer, 0, sizeof(nameBuffer));
                    memset(typeBuffer, 0, sizeof(typeBuffer));
                    hasError = false;
                    errorMessage = "";

                    // Show notification
                    g_NotificationManager.AddChange("Configurations", "Config Shared", false, true);
                } else {
                    hasError = true;
                    // Check if it's a duplicate name error
                    std::string sharedName = "Shared_" + g_ConfigSystem.shareConfigName;
                    bool isDuplicate = false;
                    for (const auto& config : g_ConfigSystem.configs) {
                        if (config.name == sharedName) {
                            isDuplicate = true;
                            break;
                        }
                    }
                    errorMessage = isDuplicate ?
                        "Shared configuration with this name already exists" :
                        "Failed to share configuration";
                }
            } catch (...) {
                hasError = true;
                errorMessage = "Unexpected error occurred while sharing configuration";
            }
        }

        if (!canCreate) {
            ImGui::PopStyleVar();
        }

        // Add cancel button
        ImGui::Spacing();
        ImGui::SetCursorPosX((ImGui::GetContentRegionAvail().x - 80) / 2);
        if (ImGui::Button("Cancel", ImVec2(80, 30))) {
            // Close popup and reset state (same as settings popup)
            gui.picker_active = false;
            g_ConfigSystem.showSharePopup = false;
            popupJustOpened = false;
            ImGui::CloseCurrentPopup();

            // Clear buffers
            memset(nameBuffer, 0, sizeof(nameBuffer));
            memset(typeBuffer, 0, sizeof(typeBuffer));
            hasError = false;
            errorMessage = "";
        }

        // Pop the style changes (same as settings popup)
        ImGui::PopStyleVar(2);  // WindowPadding, WindowRounding
        ImGui::PopStyleColor(); // PopupBg
        ImGui::EndPopup();
    }

    // Reset picker_active when popup is closed (same as settings popup)
    if (!ImGui::IsPopupOpen("##CreateConfigPopup") && (g_ConfigSystem.showSharePopup || popupJustOpened)) {
        gui.picker_active = false;
        g_ConfigSystem.showSharePopup = false;
        popupJustOpened = false;
    }
}