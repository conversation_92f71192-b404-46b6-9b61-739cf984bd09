﻿  Config.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(2,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(1861,9): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc
  HotkeySystem.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(2,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Features\Hotkeys\HotkeySystem.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Features\Hotkeys\HotkeySystem.cpp(997,9): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc
  Settings.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(2,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Settings\Settings.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Settings\Settings.cpp(88,13): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc
  Components.cpp
D:\Apps\bitcheats_cheats - New UI\Menu UI\Components\Components.cpp(1,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Components\Components.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(906,1): warning C4005: 'ICON_MS_CROP_SQUARE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(12): message : see previous definition of 'ICON_MS_CROP_SQUARE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(929,1): warning C4005: 'ICON_MS_DANGEROUS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(13): message : see previous definition of 'ICON_MS_DANGEROUS'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1039,1): warning C4005: 'ICON_MS_DIRECTIONS_CAR': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(14): message : see previous definition of 'ICON_MS_DIRECTIONS_CAR'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1446,1): warning C4005: 'ICON_MS_FOLDER': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(15): message : see previous definition of 'ICON_MS_FOLDER'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1513,1): warning C4005: 'ICON_MS_FORMAT_SIZE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(16): message : see previous definition of 'ICON_MS_FORMAT_SIZE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1568,1): warning C4005: 'ICON_MS_GAVEL': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(17): message : see previous definition of 'ICON_MS_GAVEL'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1673,1): warning C4005: 'ICON_MS_HEALING': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(18): message : see previous definition of 'ICON_MS_HEALING'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1810,1): warning C4005: 'ICON_MS_INFO': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(19): message : see previous definition of 'ICON_MS_INFO'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1842,1): warning C4005: 'ICON_MS_INVENTORY': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(20): message : see previous definition of 'ICON_MS_INVENTORY'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1988,1): warning C4005: 'ICON_MS_LINE_WEIGHT': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(21): message : see previous definition of 'ICON_MS_LINE_WEIGHT'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2011,1): warning C4005: 'ICON_MS_LOCAL_DRINK': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(22): message : see previous definition of 'ICON_MS_LOCAL_DRINK'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2418,1): warning C4005: 'ICON_MS_NOTIFICATIONS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(23): message : see previous definition of 'ICON_MS_NOTIFICATIONS'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2490,1): warning C4005: 'ICON_MS_PALETTE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(24): message : see previous definition of 'ICON_MS_PALETTE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2545,1): warning C4005: 'ICON_MS_PERSON': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(25): message : see previous definition of 'ICON_MS_PERSON'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2560,1): warning C4005: 'ICON_MS_PERSON_OFF': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(26): message : see previous definition of 'ICON_MS_PERSON_OFF'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2578,1): warning C4005: 'ICON_MS_PETS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(28): message : see previous definition of 'ICON_MS_PETS'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2757,1): warning C4005: 'ICON_MS_RADAR': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(29): message : see previous definition of 'ICON_MS_RADAR'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2872,1): warning C4005: 'ICON_MS_ROCKET': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(30): message : see previous definition of 'ICON_MS_ROCKET'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2913,1): warning C4005: 'ICON_MS_SAVE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(31): message : see previous definition of 'ICON_MS_SAVE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2929,1): warning C4005: 'ICON_MS_SCIENCE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(32): message : see previous definition of 'ICON_MS_SCIENCE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3017,1): warning C4005: 'ICON_MS_SETTINGS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(33): message : see previous definition of 'ICON_MS_SETTINGS'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3067,1): warning C4005: 'ICON_MS_SHIELD': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(34): message : see previous definition of 'ICON_MS_SHIELD'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3162,1): warning C4005: 'ICON_MS_SMART_TOY': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(35): message : see previous definition of 'ICON_MS_SMART_TOY'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3211,1): warning C4005: 'ICON_MS_SPEED': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(36): message : see previous definition of 'ICON_MS_SPEED'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3250,1): warning C4005: 'ICON_MS_SPORTS_ESPORTS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(37): message : see previous definition of 'ICON_MS_SPORTS_ESPORTS'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3331,1): warning C4005: 'ICON_MS_STRAIGHTEN': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(39): message : see previous definition of 'ICON_MS_STRAIGHTEN'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3459,1): warning C4005: 'ICON_MS_TARGET': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(40): message : see previous definition of 'ICON_MS_TARGET'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3539,1): warning C4005: 'ICON_MS_TIMER': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(41): message : see previous definition of 'ICON_MS_TIMER'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3613,1): warning C4005: 'ICON_MS_TRANSLATE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(42): message : see previous definition of 'ICON_MS_TRANSLATE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3619,1): warning C4005: 'ICON_MS_TRENDING_FLAT': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(43): message : see previous definition of 'ICON_MS_TRENDING_FLAT'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3620,1): warning C4005: 'ICON_MS_TRENDING_UP': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(44): message : see previous definition of 'ICON_MS_TRENDING_UP'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3631,1): warning C4005: 'ICON_MS_TUNE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(45): message : see previous definition of 'ICON_MS_TUNE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3760,1): warning C4005: 'ICON_MS_VISIBILITY': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(46): message : see previous definition of 'ICON_MS_VISIBILITY'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3814,1): warning C4005: 'ICON_MS_WATER_DROP': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(48): message : see previous definition of 'ICON_MS_WATER_DROP'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3916,1): warning C4005: 'ICON_MS_ZOOM_IN': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(50): message : see previous definition of 'ICON_MS_ZOOM_IN'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(752,1): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(726): message : see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>> *std::vector<std::string,std::allocator<std::string>>::_Emplace_reallocate<_Ty>(std::basic_string<char,std::char_traits<char>,std::allocator<char>> *const ,_Ty &&)' being compiled
          with
          [
              _Ty=std::basic_string<char,std::char_traits<char>,std::allocator<char>>
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(734): message : see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>> *std::vector<std::string,std::allocator<std::string>>::_Emplace_reallocate<_Ty>(std::basic_string<char,std::char_traits<char>,std::allocator<char>> *const ,_Ty &&)' being compiled
          with
          [
              _Ty=std::basic_string<char,std::char_traits<char>,std::allocator<char>>
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(748): message : see reference to function template instantiation '_Ty &std::vector<_Ty,std::allocator<_Ty>>::emplace_back<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>(std::basic_string<char,std::char_traits<char>,std::allocator<char>> &&)' being compiled
          with
          [
              _Ty=std::string
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(746): message : while compiling class template member function 'void std::vector<std::string,std::allocator<std::string>>::push_back(_Ty &&)'
          with
          [
              _Ty=std::string
          ]
D:\Apps\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(147): message : see reference to function template instantiation 'void std::vector<std::string,std::allocator<std::string>>::push_back(_Ty &&)' being compiled
          with
          [
              _Ty=std::string
          ]
D:\Apps\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(36): message : see reference to class template instantiation 'std::vector<std::string,std::allocator<std::string>>' being compiled
  nav_elements.cpp
D:\Apps\bitcheats_cheats - New UI\Menu UI\Components\Animation\ImAnim\custom_functions.h(6,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(906,1): warning C4005: 'ICON_MS_CROP_SQUARE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(12): message : see previous definition of 'ICON_MS_CROP_SQUARE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(929,1): warning C4005: 'ICON_MS_DANGEROUS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(13): message : see previous definition of 'ICON_MS_DANGEROUS'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1039,1): warning C4005: 'ICON_MS_DIRECTIONS_CAR': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(14): message : see previous definition of 'ICON_MS_DIRECTIONS_CAR'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1446,1): warning C4005: 'ICON_MS_FOLDER': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(15): message : see previous definition of 'ICON_MS_FOLDER'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1513,1): warning C4005: 'ICON_MS_FORMAT_SIZE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(16): message : see previous definition of 'ICON_MS_FORMAT_SIZE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1568,1): warning C4005: 'ICON_MS_GAVEL': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(17): message : see previous definition of 'ICON_MS_GAVEL'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1673,1): warning C4005: 'ICON_MS_HEALING': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(18): message : see previous definition of 'ICON_MS_HEALING'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1810,1): warning C4005: 'ICON_MS_INFO': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(19): message : see previous definition of 'ICON_MS_INFO'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1842,1): warning C4005: 'ICON_MS_INVENTORY': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(20): message : see previous definition of 'ICON_MS_INVENTORY'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1988,1): warning C4005: 'ICON_MS_LINE_WEIGHT': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(21): message : see previous definition of 'ICON_MS_LINE_WEIGHT'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2011,1): warning C4005: 'ICON_MS_LOCAL_DRINK': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(22): message : see previous definition of 'ICON_MS_LOCAL_DRINK'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2418,1): warning C4005: 'ICON_MS_NOTIFICATIONS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(23): message : see previous definition of 'ICON_MS_NOTIFICATIONS'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2490,1): warning C4005: 'ICON_MS_PALETTE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(24): message : see previous definition of 'ICON_MS_PALETTE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2545,1): warning C4005: 'ICON_MS_PERSON': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(25): message : see previous definition of 'ICON_MS_PERSON'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2560,1): warning C4005: 'ICON_MS_PERSON_OFF': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(26): message : see previous definition of 'ICON_MS_PERSON_OFF'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2578,1): warning C4005: 'ICON_MS_PETS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(28): message : see previous definition of 'ICON_MS_PETS'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2757,1): warning C4005: 'ICON_MS_RADAR': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(29): message : see previous definition of 'ICON_MS_RADAR'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2872,1): warning C4005: 'ICON_MS_ROCKET': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(30): message : see previous definition of 'ICON_MS_ROCKET'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2913,1): warning C4005: 'ICON_MS_SAVE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(31): message : see previous definition of 'ICON_MS_SAVE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2929,1): warning C4005: 'ICON_MS_SCIENCE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(32): message : see previous definition of 'ICON_MS_SCIENCE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3017,1): warning C4005: 'ICON_MS_SETTINGS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(33): message : see previous definition of 'ICON_MS_SETTINGS'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3067,1): warning C4005: 'ICON_MS_SHIELD': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(34): message : see previous definition of 'ICON_MS_SHIELD'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3162,1): warning C4005: 'ICON_MS_SMART_TOY': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(35): message : see previous definition of 'ICON_MS_SMART_TOY'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3211,1): warning C4005: 'ICON_MS_SPEED': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(36): message : see previous definition of 'ICON_MS_SPEED'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3250,1): warning C4005: 'ICON_MS_SPORTS_ESPORTS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(37): message : see previous definition of 'ICON_MS_SPORTS_ESPORTS'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3331,1): warning C4005: 'ICON_MS_STRAIGHTEN': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(39): message : see previous definition of 'ICON_MS_STRAIGHTEN'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3459,1): warning C4005: 'ICON_MS_TARGET': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(40): message : see previous definition of 'ICON_MS_TARGET'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3539,1): warning C4005: 'ICON_MS_TIMER': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(41): message : see previous definition of 'ICON_MS_TIMER'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3613,1): warning C4005: 'ICON_MS_TRANSLATE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(42): message : see previous definition of 'ICON_MS_TRANSLATE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3619,1): warning C4005: 'ICON_MS_TRENDING_FLAT': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(43): message : see previous definition of 'ICON_MS_TRENDING_FLAT'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3620,1): warning C4005: 'ICON_MS_TRENDING_UP': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(44): message : see previous definition of 'ICON_MS_TRENDING_UP'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3631,1): warning C4005: 'ICON_MS_TUNE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(45): message : see previous definition of 'ICON_MS_TUNE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3760,1): warning C4005: 'ICON_MS_VISIBILITY': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(46): message : see previous definition of 'ICON_MS_VISIBILITY'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3814,1): warning C4005: 'ICON_MS_WATER_DROP': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(48): message : see previous definition of 'ICON_MS_WATER_DROP'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3916,1): warning C4005: 'ICON_MS_ZOOM_IN': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(50): message : see previous definition of 'ICON_MS_ZOOM_IN'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(1369,17): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc
  GUI.cpp
D:\Apps\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(1,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(906,1): warning C4005: 'ICON_MS_CROP_SQUARE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(12): message : see previous definition of 'ICON_MS_CROP_SQUARE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(929,1): warning C4005: 'ICON_MS_DANGEROUS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(13): message : see previous definition of 'ICON_MS_DANGEROUS'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1039,1): warning C4005: 'ICON_MS_DIRECTIONS_CAR': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(14): message : see previous definition of 'ICON_MS_DIRECTIONS_CAR'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1446,1): warning C4005: 'ICON_MS_FOLDER': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(15): message : see previous definition of 'ICON_MS_FOLDER'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1513,1): warning C4005: 'ICON_MS_FORMAT_SIZE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(16): message : see previous definition of 'ICON_MS_FORMAT_SIZE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1568,1): warning C4005: 'ICON_MS_GAVEL': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(17): message : see previous definition of 'ICON_MS_GAVEL'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1673,1): warning C4005: 'ICON_MS_HEALING': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(18): message : see previous definition of 'ICON_MS_HEALING'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1810,1): warning C4005: 'ICON_MS_INFO': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(19): message : see previous definition of 'ICON_MS_INFO'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1842,1): warning C4005: 'ICON_MS_INVENTORY': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(20): message : see previous definition of 'ICON_MS_INVENTORY'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1988,1): warning C4005: 'ICON_MS_LINE_WEIGHT': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(21): message : see previous definition of 'ICON_MS_LINE_WEIGHT'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2011,1): warning C4005: 'ICON_MS_LOCAL_DRINK': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(22): message : see previous definition of 'ICON_MS_LOCAL_DRINK'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2418,1): warning C4005: 'ICON_MS_NOTIFICATIONS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(23): message : see previous definition of 'ICON_MS_NOTIFICATIONS'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2490,1): warning C4005: 'ICON_MS_PALETTE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(24): message : see previous definition of 'ICON_MS_PALETTE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2545,1): warning C4005: 'ICON_MS_PERSON': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(25): message : see previous definition of 'ICON_MS_PERSON'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2560,1): warning C4005: 'ICON_MS_PERSON_OFF': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(26): message : see previous definition of 'ICON_MS_PERSON_OFF'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2578,1): warning C4005: 'ICON_MS_PETS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(28): message : see previous definition of 'ICON_MS_PETS'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2757,1): warning C4005: 'ICON_MS_RADAR': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(29): message : see previous definition of 'ICON_MS_RADAR'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2872,1): warning C4005: 'ICON_MS_ROCKET': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(30): message : see previous definition of 'ICON_MS_ROCKET'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2913,1): warning C4005: 'ICON_MS_SAVE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(31): message : see previous definition of 'ICON_MS_SAVE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2929,1): warning C4005: 'ICON_MS_SCIENCE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(32): message : see previous definition of 'ICON_MS_SCIENCE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3017,1): warning C4005: 'ICON_MS_SETTINGS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(33): message : see previous definition of 'ICON_MS_SETTINGS'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3067,1): warning C4005: 'ICON_MS_SHIELD': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(34): message : see previous definition of 'ICON_MS_SHIELD'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3162,1): warning C4005: 'ICON_MS_SMART_TOY': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(35): message : see previous definition of 'ICON_MS_SMART_TOY'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3211,1): warning C4005: 'ICON_MS_SPEED': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(36): message : see previous definition of 'ICON_MS_SPEED'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3250,1): warning C4005: 'ICON_MS_SPORTS_ESPORTS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(37): message : see previous definition of 'ICON_MS_SPORTS_ESPORTS'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3331,1): warning C4005: 'ICON_MS_STRAIGHTEN': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(39): message : see previous definition of 'ICON_MS_STRAIGHTEN'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3459,1): warning C4005: 'ICON_MS_TARGET': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(40): message : see previous definition of 'ICON_MS_TARGET'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3539,1): warning C4005: 'ICON_MS_TIMER': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(41): message : see previous definition of 'ICON_MS_TIMER'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3613,1): warning C4005: 'ICON_MS_TRANSLATE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(42): message : see previous definition of 'ICON_MS_TRANSLATE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3619,1): warning C4005: 'ICON_MS_TRENDING_FLAT': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(43): message : see previous definition of 'ICON_MS_TRENDING_FLAT'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3620,1): warning C4005: 'ICON_MS_TRENDING_UP': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(44): message : see previous definition of 'ICON_MS_TRENDING_UP'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3631,1): warning C4005: 'ICON_MS_TUNE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(45): message : see previous definition of 'ICON_MS_TUNE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3760,1): warning C4005: 'ICON_MS_VISIBILITY': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(46): message : see previous definition of 'ICON_MS_VISIBILITY'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3814,1): warning C4005: 'ICON_MS_WATER_DROP': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(48): message : see previous definition of 'ICON_MS_WATER_DROP'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3916,1): warning C4005: 'ICON_MS_ZOOM_IN': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(50): message : see previous definition of 'ICON_MS_ZOOM_IN'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(67,1): warning C4005: 'ICON_KEYBOARD_LINE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(56): message : see previous definition of 'ICON_KEYBOARD_LINE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(71,1): warning C4005: 'ICON_SETTINGS_LINE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(45): message : see previous definition of 'ICON_SETTINGS_LINE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(78,1): warning C4005: 'ICON_DOWNLOAD_LINE': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(39): message : see previous definition of 'ICON_DOWNLOAD_LINE'
D:\Apps\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(339,46): warning C4838: conversion from 'int' to 'ImWchar' requires a narrowing conversion
D:\Apps\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(1768,17): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc
  imgui_widgets.cpp
D:\Apps\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(3,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_widgets.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(752,1): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(726): message : see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>> *std::vector<std::string,std::allocator<std::string>>::_Emplace_reallocate<_Ty>(std::basic_string<char,std::char_traits<char>,std::allocator<char>> *const ,_Ty &&)' being compiled
          with
          [
              _Ty=std::basic_string<char,std::char_traits<char>,std::allocator<char>>
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(734): message : see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>> *std::vector<std::string,std::allocator<std::string>>::_Emplace_reallocate<_Ty>(std::basic_string<char,std::char_traits<char>,std::allocator<char>> *const ,_Ty &&)' being compiled
          with
          [
              _Ty=std::basic_string<char,std::char_traits<char>,std::allocator<char>>
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(748): message : see reference to function template instantiation '_Ty &std::vector<_Ty,std::allocator<_Ty>>::emplace_back<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>(std::basic_string<char,std::char_traits<char>,std::allocator<char>> &&)' being compiled
          with
          [
              _Ty=std::string
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(746): message : while compiling class template member function 'void std::vector<std::string,std::allocator<std::string>>::push_back(_Ty &&)'
          with
          [
              _Ty=std::string
          ]
D:\Apps\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(147): message : see reference to function template instantiation 'void std::vector<std::string,std::allocator<std::string>>::push_back(_Ty &&)' being compiled
          with
          [
              _Ty=std::string
          ]
D:\Apps\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(36): message : see reference to class template instantiation 'std::vector<std::string,std::allocator<std::string>>' being compiled
  main.cpp
D:\Apps\bitcheats_cheats - New UI\main.cpp(16,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
D:\Apps\bitcheats_cheats - New UI\main.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\ostream(743,1): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc
D:\Apps\bitcheats_cheats - New UI\main.cpp(569): message : see reference to function template instantiation 'std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basic_ostream<char,std::char_traits<char>> &,const char *)' being compiled
LINK : warning LNK4044: unrecognized option '/bigobj'; ignored
LINK : warning LNK4044: unrecognized option '/utf-8'; ignored
  Generating code
  Not all modules are compiled with -Gy (function comdat), build without incremental LTCG.
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Include\utils.h(35): warning C4715: '<lambda_68805d3f38fb1b3b9ad20302bfb59b0f>::operator()': not all control paths return a value
D:\Apps\bitcheats_cheats - New UI\main.cpp(201): warning C4715: 'SetupOverlayWindow': not all control paths return a value
D:\Apps\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(2195): warning C4715: 'SaveAimSettingsToFile': not all control paths return a value
  Finished generating code
  BitCheats.vcxproj -> D:\Apps\bitcheats_cheats - New UI\x64\Release\BitCheats.dll
  'pwsh.exe' is not recognized as an internal or external command,
  operable program or batch file.
